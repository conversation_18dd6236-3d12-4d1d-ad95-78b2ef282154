import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { useSharedData } from '../../contexts/SharedDataContext';

/**
 * Simple SharedDataDropdown - follows your exact working pattern
 */
const SharedDataDropdown = ({
  dataType,
  label,
  name,
  value,
  onChange,
  disabled = false,
  error,
  helperText,
  showEmptyOption = true,
  filterBy,
  filterValue,
  ...props
}) => {
  const sharedData = useSharedData();

  // Get the data - exactly like your pattern
  const rawData = sharedData[dataType] || [];
  const isLoading = sharedData.loading;

  // Ensure rawData is an array
  const dataArray = Array.isArray(rawData) ? rawData : [];

  // Debug logging
  if (!Array.isArray(rawData) && rawData !== undefined) {
    console.warn(`SharedDataDropdown: ${dataType} is not an array:`, rawData);
  }

  // Apply filtering if needed
  let filteredOptions = dataArray;
  if (filterBy && filterValue) {
    filteredOptions = dataArray.filter(item =>
      String(item[filterBy]) === String(filterValue)
    );
  }

  const options = filteredOptions;

  // Ensure value is properly handled
  const normalizedValue = value || '';
  const valueExists = options.some(opt => String(opt.id) === String(normalizedValue));

  // Debug the value prop (only when value is set)
  if (normalizedValue) {
    console.log(`🔍 SharedDataDropdown ${dataType}:`, {
      originalValue: value,
      normalizedValue,
      disabled,
      optionsCount: options.length,
      valueExists,
      selectedOption: options.find(opt => String(opt.id) === String(normalizedValue))
    });
  }

  return (
    <FormControl fullWidth error={Boolean(error)} {...props}>
      <InputLabel>{label}</InputLabel>
      <Select
        name={name}
        value={normalizedValue}
        onChange={onChange}
        label={label}
        disabled={disabled}
        displayEmpty
      >
        {/* Empty option for when no value is selected */}
        <MenuItem value="">
          <em>{label}</em>
        </MenuItem>
        {isLoading ? (
          <MenuItem disabled>Loading {label.toLowerCase()}...</MenuItem>
        ) : options.length === 0 ? (
          <MenuItem disabled>No {label.toLowerCase()} available from database</MenuItem>
        ) : (
          // Dynamic options from DATABASE - real data
          options.map((option) => (
            <MenuItem key={option.id} value={option.id}>
              {option.name}
            </MenuItem>
          ))
        )}
      </Select>
      {error && <FormHelperText>{error}</FormHelperText>}
      {helperText && !error && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default SharedDataDropdown;

:root {
  font-family: '<PERSON><PERSON>', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

.id-card {
  width: 85.6mm;
  height: 54mm;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  background-color: #fff;
  margin: 20px auto;
}

.id-card-front {
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.id-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.id-card-logo {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}

.id-card-title {
  flex: 1;
}

.id-card-title h2 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.id-card-title h3 {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.id-card-content {
  display: flex;
}

.id-card-photo {
  width: 80px;
  height: 100px;
  background-color: #f0f0f0;
  margin-right: 12px;
  border: 1px solid #ddd;
}

.id-card-details {
  flex: 1;
}

.id-card-details p {
  margin: 4px 0;
  font-size: 10px;
  color: #333;
}

.id-card-details .label {
  font-weight: bold;
  color: #666;
}

.id-card-footer {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.id-card-qr {
  width: 40px;
  height: 40px;
  background-color: #f0f0f0;
}

.id-card-number {
  font-size: 10px;
  color: #666;
}

.id-card-back {
  padding: 16px;
}

.id-card-back h3 {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #333;
  text-align: center;
}

.id-card-back p {
  margin: 8px 0;
  font-size: 10px;
  color: #333;
}

.id-card-signature {
  margin-top: 16px;
  border-top: 1px dashed #ddd;
  padding-top: 8px;
  text-align: center;
  font-size: 10px;
  color: #666;
}

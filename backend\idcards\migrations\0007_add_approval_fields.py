# Generated migration for ID card approval fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('idcards', '0006_alter_idcard_citizen'),
    ]

    operations = [
        migrations.AddField(
            model_name='idcard',
            name='approval_pattern',
            field=models.CharField(blank=True, help_text="Pattern applied when approved (e.g., 'diagonal_stripes', 'watermark')", max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='approval_comment',
            field=models.TextField(blank=True, help_text='Comment from kebele leader during approval', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='submitted_for_approval_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was submitted for approval', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='approved_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was approved', null=True),
        ),
    ]

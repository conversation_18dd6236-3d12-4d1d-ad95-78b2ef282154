# Generated manually for creating related tables

from django.db import migrations, models
import django.db.models.deletion


def citizen_photo_path(instance, filename):
    """Generate file path for citizen photos"""
    return f"citizens/{instance.citizen.get_directory_path()}/photos/{filename}"


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0005_remove_old_fields'),
    ]

    operations = [
        # Create EmergencyContact model
        migrations.CreateModel(
            name='EmergencyContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.Char<PERSON>ield(max_length=100)),
                ('first_name_am', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('relationship', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the emergency contact is a resident of the city.')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_contacts', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_emergency_contact', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Emergency Contact',
                'verbose_name_plural': 'Emergency Contacts',
            },
        ),
        
        # Create Parent model
        migrations.CreateModel(
            name='Parent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('gender', models.CharField(max_length=10)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the parent is a resident of the city.')),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parents', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_parent', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Parent',
                'verbose_name_plural': 'Parents',
            },
        ),
        
        # Create Child model
        migrations.CreateModel(
            name='Child',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, max_length=10, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the child is a resident of the city.')),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='children', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_child', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Child',
                'verbose_name_plural': 'Children',
            },
        ),
    ]

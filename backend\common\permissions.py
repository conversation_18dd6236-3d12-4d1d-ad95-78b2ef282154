from rest_framework import permissions
from django.db import connection


class RoleBasedPermission(permissions.BasePermission):
    """
    Base permission class for role-based permissions.
    """
    allowed_roles = []
    tenant_type_restriction = None

    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and superadmins have all permissions
        if user.is_superuser or user.role == 'superadmin':
            return True

        # Check if user's role is in allowed roles
        if self.allowed_roles and user.role not in self.allowed_roles:
            return False

        # Check tenant type restriction if applicable
        if self.tenant_type_restriction and user.tenant:
            if user.tenant.type != self.tenant_type_restriction:
                return False

        return True


class ClerkPermission(RoleBasedPermission):
    """
    Permission class for clerks.
    Clerks can only be in kebele tenants.
    """
    allowed_roles = ['clerk']
    tenant_type_restriction = 'kebele'


class KebeleAdminPermission(RoleBasedPermission):
    """
    Permission class for kebele admins.
    Kebele admins can only be in kebele tenants.
    """
    allowed_roles = ['kebele_admin']
    tenant_type_restriction = 'kebele'


class KebeleLeaderPermission(RoleBasedPermission):
    """
    Permission class for kebele leaders.
    Kebele leaders can only be in kebele tenants.
    """
    allowed_roles = ['kebele_leader']
    tenant_type_restriction = 'kebele'


class SubcityAdminPermission(RoleBasedPermission):
    """
    Permission class for subcity admins.
    Subcity admins can only be in subcity tenants.
    """
    allowed_roles = ['subcity_admin']
    tenant_type_restriction = 'subcity'


class CityAdminPermission(RoleBasedPermission):
    """
    Permission class for city admins.
    City admins can only be in city tenants.
    """
    allowed_roles = ['city_admin']
    tenant_type_restriction = 'city'


class SuperAdminPermission(RoleBasedPermission):
    """
    Permission class for super admins.
    Super admins have access to all tenants.
    """
    allowed_roles = ['superadmin']


class IsClerkOrAdmin(permissions.BasePermission):
    """
    Permission class for actions that clerks and admins can perform.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and all admin roles have permission
        if user.is_superuser or user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin', 'clerk']:
            return True

        return False


class IsKebeleAdminOrHigher(permissions.BasePermission):
    """
    Permission class for actions that kebele admins and higher roles can perform.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and admin roles (except clerk) have permission
        if user.is_superuser or user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin']:
            return True

        return False


class IsSubcityAdminOrHigher(permissions.BasePermission):
    """
    Permission class for actions that subcity admins and higher roles can perform.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and higher admin roles have permission
        if user.is_superuser or user.role in ['superadmin', 'city_admin', 'subcity_admin']:
            return True

        return False


class IsCityAdminOrHigher(permissions.BasePermission):
    """
    Permission class for actions that city admins and higher roles can perform.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers, superadmins, and city admins have permission
        if user.is_superuser or user.role in ['superadmin', 'city_admin']:
            return True

        return False


class IsSuperAdmin(permissions.BasePermission):
    """
    Permission class for actions that only super admins can perform.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and superadmins have permission
        if user.is_superuser or user.role == 'superadmin':
            return True

        return False


class CanManageCitizens(permissions.BasePermission):
    """
    Permission class for managing citizens.
    Clerks can create and view citizens.
    Kebele admins and higher can view citizens.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and all admin roles have permission to view
        if user.is_superuser or user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk']:
            # For safe methods (GET, HEAD, OPTIONS), all roles have permission
            if request.method in permissions.SAFE_METHODS:
                return True

            # CRITICAL SECURITY: Only clerks at kebele level can create/edit citizens
            # This ensures citizen data integrity and proper workflow
            if user.role == 'clerk' or user.is_superuser or user.role == 'superadmin':
                return True

            return False

        return False


class CanManageIDCards(permissions.BasePermission):
    """
    Permission class for managing ID cards.
    Clerks can create and view ID cards.
    Kebele leaders and kebele admins can approve/reject ID cards.
    All admin roles can view ID cards.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and all admin roles have permission to view
        if user.is_superuser or user.role in ['superadmin', 'city_admin', 'subcity_admin', 'kebele_admin', 'kebele_leader', 'clerk']:
            # For safe methods (GET, HEAD, OPTIONS), all roles have permission
            if request.method in permissions.SAFE_METHODS:
                return True

            # For unsafe methods (POST, PUT, PATCH, DELETE), check specific permissions
            if view.action == 'create' and user.role == 'clerk':
                return True

            # Approval actions - clerks can submit, kebele leaders and admins can approve
            if view.action in ['update_status', 'approval_action'] and (user.role in ['clerk', 'kebele_leader', 'kebele_admin', 'subcity_admin', 'superadmin'] or user.is_superuser):
                return True

            return False

        return False


class CanApproveIDCards(permissions.BasePermission):
    """
    Permission class for ID card approval actions.
    - Clerks can submit ID cards for approval
    - Kebele leaders and kebele admins can approve/reject ID cards
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers have full permission
        if user.is_superuser:
            return True

        # Clerks can submit for approval, kebele leaders and admins can approve/reject
        return user.role in ['clerk', 'kebele_leader', 'kebele_admin', 'subcity_admin', 'superadmin']


class CanPrintIDCards(permissions.BasePermission):
    """
    Permission class for ID card printing actions.
    CRITICAL SECURITY: Only subcity admins and higher can print ID cards.
    This ensures printing is centralized at subcity level for security.
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers have full permission
        if user.is_superuser:
            return True

        # CRITICAL SECURITY: Only subcity admins and higher can print
        return user.role in ['subcity_admin', 'city_admin', 'superadmin']


class CanManageUsers(permissions.BasePermission):
    """
    Permission class for managing users.

    - Kebele admins can only manage clerk users in their kebele
    - Subcity admins can manage kebele admins and clerks in their subcity and child kebeles
    - City admins can manage subcity admins, kebele admins, and clerks in their city and child subcities/kebeles
    - Super admins can manage all users
    """
    def has_permission(self, request, view):
        user = request.user

        # Check if user is authenticated
        if not user or not user.is_authenticated:
            return False

        # Superusers and superadmins have full permission
        if user.is_superuser or getattr(user, 'role', None) == 'superadmin':
            return True

        # For safe methods (GET, HEAD, OPTIONS), all admin roles have permission
        if request.method in permissions.SAFE_METHODS:
            if getattr(user, 'role', None) in ['city_admin', 'subcity_admin', 'kebele_admin']:
                return True

        # For unsafe methods (POST, PUT, PATCH, DELETE), check role-specific permissions
        if getattr(user, 'role', None) == 'city_admin':
            # City admins can create/edit subcity admins
            return True
        elif getattr(user, 'role', None) == 'subcity_admin':
            # Subcity admins can create/edit kebele admins
            return True
        elif getattr(user, 'role', None) == 'kebele_admin':
            # Kebele admins can create/edit clerks
            return True

        return False

    def has_object_permission(self, request, view, obj):
        user = request.user

        # Superusers and superadmins have full permission
        if user.is_superuser or getattr(user, 'role', None) == 'superadmin':
            return True

        # Users can always view/edit themselves
        if obj.id == user.id:
            return True

        # Check role-based permissions
        if getattr(user, 'role', None) == 'city_admin':
            # City admins can manage subcity admins, kebele admins, and clerks in their city hierarchy
            if getattr(obj, 'role', None) in ['subcity_admin', 'kebele_admin', 'clerk']:
                # Check if the user belongs to the city's hierarchy
                if getattr(obj, 'tenant', None) and getattr(user, 'tenant', None):
                    if obj.tenant == user.tenant:  # Same tenant
                        return True
                    if hasattr(obj.tenant, 'parent') and obj.tenant.parent == user.tenant:  # Direct child (subcity)
                        return True
                    if hasattr(obj.tenant, 'parent') and obj.tenant.parent and hasattr(obj.tenant.parent, 'parent') and obj.tenant.parent.parent == user.tenant:  # Grandchild (kebele)
                        return True
        elif getattr(user, 'role', None) == 'subcity_admin':
            # Subcity admins can manage kebele admins and clerks in their subcity hierarchy
            if getattr(obj, 'role', None) in ['kebele_admin', 'clerk']:
                # Check if the user belongs to the subcity's hierarchy
                if getattr(obj, 'tenant', None) and getattr(user, 'tenant', None):
                    if obj.tenant == user.tenant:  # Same tenant
                        return True
                    if hasattr(obj.tenant, 'parent') and obj.tenant.parent == user.tenant:  # Direct child (kebele)
                        return True
        elif getattr(user, 'role', None) == 'kebele_admin':
            # Kebele admins can only manage clerks in their kebele
            if getattr(obj, 'role', None) == 'clerk' and getattr(obj, 'tenant', None) == getattr(user, 'tenant', None):
                return True

        return False

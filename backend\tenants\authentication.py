from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from django.contrib.auth import get_user_model
from tenants.models import Tenant
import jwt


class TenantAwareJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication that populates user.tenant from JWT token.
    """
    
    def authenticate(self, request):
        """
        Authenticate the request and populate user.tenant from JWT token.
        """
        result = super().authenticate(request)
        
        if result is not None:
            user, validated_token = result
            
            # Extract tenant information from the token
            tenant_id = validated_token.get('tenant_id')
            
            if tenant_id:
                try:
                    tenant = Tenant.objects.get(id=tenant_id)
                    # Set tenant attribute on user object
                    user.tenant = tenant
                except Tenant.DoesNotExist:
                    # If tenant doesn't exist, continue without setting it
                    pass
            
            return user, validated_token
        
        return result

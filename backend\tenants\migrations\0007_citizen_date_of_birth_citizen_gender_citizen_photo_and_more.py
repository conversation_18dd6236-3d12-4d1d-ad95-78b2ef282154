# Generated by Django 4.2.7 on 2025-05-26 12:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0006_alter_parent_options_parent_citizen_parent_email_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='gender',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='photo',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='employee_type',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='employment',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='citizen',
            name='kebele',
            field=models.Integer<PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='ketena',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='marital_status',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='nationality',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='organization_name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='region',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='religion',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='status',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='subcity',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]

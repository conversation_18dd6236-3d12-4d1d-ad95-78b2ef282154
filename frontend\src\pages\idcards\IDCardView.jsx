import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Divider,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  Print as PrintIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  ArrowBack as BackIcon,
  CreditCard as FrontIcon,
  CreditCardOff as BackSideIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import { canApproveIDCards } from '../../utils/permissions';
import { usePermissions } from '../../hooks/usePermissions';
import { getSecurityPatternClass, getPatternDescription } from '../../utils/securityPatterns';
import '../../styles/securityPatterns.css';

// No more mock data - using real API calls

/**
 * Simple QR Code component using QR Server API
 */
const QRCode = ({ value, size = 80 }) => {
  const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(value)}`;

  return (
    <img
      src={qrUrl}
      alt="QR Code"
      style={{
        width: size,
        height: size,
        border: '2px solid #ccc',
        borderRadius: 4
      }}
      onError={(e) => {
        // Fallback to placeholder if QR service fails
        e.target.style.display = 'none';
        e.target.nextSibling.style.display = 'flex';
      }}
    />
  );
};

/**
 * Get tenant ID from multiple sources for reliability
 */
const getTenantId = () => {
  console.log('🔍 Getting tenant ID...');

  // First try to get from localStorage user object
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.tenant_id) {
      console.log('🔍 From localStorage user:', user.tenant_id);
      return user.tenant_id;
    }
  } catch (e) {
    console.warn('Could not parse stored user data');
  }

  // Fallback: Get from JWT token
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      const base64Url = accessToken.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      const tokenData = JSON.parse(jsonPayload);
      console.log('🔍 From JWT token:', tokenData.tenant_id);
      return tokenData.tenant_id;
    }
  } catch (e) {
    console.warn('Could not decode JWT token');
  }

  console.log('❌ No tenant ID found');
  return null;
};

const IDCardView = () => {
  const { id, tenantId } = useParams(); // Support both /idcards/:id and /tenants/:tenantId/idcards/:id
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const [idCard, setIdCard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [comment, setComment] = useState('');
  const [showFront, setShowFront] = useState(true); // Toggle between front and back

  // Auto-open print preview if 'print' parameter is present
  useEffect(() => {
    if (searchParams.get('print') === 'true' && idCard && hasPermission('print_idcards')) {
      setPrintPreviewOpen(true);
      // Remove the print parameter from URL
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.delete('print');
        return newParams;
      });
    }
  }, [idCard, searchParams, hasPermission, setSearchParams]);

  useEffect(() => {
    const fetchIDCard = async () => {
      try {
        setLoading(true);
        setError('');

        console.log('🔍 Fetching ID card with ID:', id);

        // Use tenant ID from URL params if available (cross-tenant navigation), otherwise use current user's tenant
        const effectiveTenantId = tenantId || getTenantId();

        if (!effectiveTenantId) {
          throw new Error('No tenant ID found. Please log in again.');
        }

        console.log('🔍 Using tenant ID:', effectiveTenantId);
        console.log('🔍 Cross-tenant context (tenantId from URL):', tenantId);

        let response;
        let idCardData;

        // If we have tenantId from URL params, we're in cross-tenant context
        if (tenantId) {
          console.log('🔄 Using cross-tenant detail endpoint for subcity admin access');
          try {
            // Try cross-tenant detail endpoint first
            response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/cross_tenant_detail/`);
            idCardData = response.data;
            console.log('✅ ID card fetched via cross-tenant endpoint');
          } catch (crossTenantError) {
            console.warn('⚠️ Cross-tenant endpoint failed, trying regular endpoint:', crossTenantError);
            // Fallback to regular endpoint
            response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/`);
            idCardData = response.data;
          }
        } else {
          console.log('🔄 Using regular tenant-specific endpoint');
          // Regular tenant-specific endpoint for same-tenant access
          response = await axios.get(`/api/tenants/${effectiveTenantId}/idcards/${id}/`);
          idCardData = response.data;
        }

        console.log('✅ ID card fetched successfully:', idCardData);
        console.log('🔍 ID card citizen info:', {
          citizen_id: idCardData.citizen_id,
          citizen_name: idCardData.citizen_name,
          citizen_digital_id: idCardData.citizen_digital_id,
          card_number: idCardData.card_number
        });

        // If we have a citizen_id, fetch additional citizen details for photo and other info
        if (idCardData.citizen_id && !idCardData.citizen_details) {
          try {
            console.log('🔍 Fetching additional citizen details for ID:', idCardData.citizen_id);

            let citizenData;
            if (tenantId && idCardData.kebele_tenant) {
              // For cross-tenant context, use the kebele tenant ID where the citizen is stored
              console.log('🔄 Fetching citizen from kebele tenant:', idCardData.kebele_tenant.id);
              const citizenResponse = await axios.get(`/api/tenants/${idCardData.kebele_tenant.id}/citizens/${idCardData.citizen_id}/`);
              citizenData = citizenResponse.data;
            } else {
              // Regular same-tenant access
              const citizenResponse = await axios.get(`/api/tenants/${effectiveTenantId}/citizens/${idCardData.citizen_id}/`);
              citizenData = citizenResponse.data;
            }

            // Merge citizen data with ID card data
            const enhancedIdCard = {
              ...idCardData,
              citizen_photo: citizenData.photo,
              citizen_gender: citizenData.gender,
              citizen_date_of_birth: citizenData.date_of_birth,
              citizen_place_of_birth: citizenData.place_of_birth || citizenData.subcity || citizenData.kebele,
              citizen_blood_type: citizenData.blood_type,
              // Add Amharic names
              citizen_first_name_am: citizenData.first_name_am,
              citizen_middle_name_am: citizenData.middle_name_am,
              citizen_last_name_am: citizenData.last_name_am,
              // Add English names for fallback
              citizen_first_name: citizenData.first_name,
              citizen_middle_name: citizenData.middle_name,
              citizen_last_name: citizenData.last_name
            };

            // Get tenant hierarchy for dynamic header
            console.log('🔍 Getting tenant hierarchy...');

            // Try to get from user context first
            let tenantHierarchy = {
              current_tenant: 'ገብርኤል',
              parent_tenant: 'ዞብል',
              city_tenant: 'ጎንደር'
            };

            if (user?.tenant_name) {
              tenantHierarchy.current_tenant = user.tenant_name;
            }
            if (user?.parent_tenant_name) {
              tenantHierarchy.parent_tenant = user.parent_tenant_name;
            }
            if (user?.city_tenant_name) {
              tenantHierarchy.city_tenant = user.city_tenant_name;
            }

            // If not available in user context, try to fetch from API
            if (!user?.tenant_name) {
              try {
                console.log('🔍 Fetching tenant info from API...');
                const tenantResponse = await axios.get(`/api/tenants/${effectiveTenantId}/info/`);
                const tenantData = tenantResponse.data;

                tenantHierarchy = {
                  current_tenant: tenantData.name || tenantHierarchy.current_tenant,
                  parent_tenant: tenantData.parent_name || tenantHierarchy.parent_tenant,
                  city_tenant: tenantData.city_name || tenantHierarchy.city_tenant
                };

                console.log('✅ Tenant hierarchy from API:', tenantHierarchy);
              } catch (hierarchyError) {
                console.warn('⚠️ Could not fetch tenant hierarchy, using fallbacks:', hierarchyError);
              }
            } else {
              console.log('✅ Tenant hierarchy from user context:', tenantHierarchy);
            }

            enhancedIdCard.tenant_hierarchy = tenantHierarchy;

            console.log('✅ Enhanced ID card with citizen details:', enhancedIdCard);
            console.log('🔒 Security pattern status:', {
              has_kebele_pattern: enhancedIdCard.has_kebele_pattern,
              has_subcity_pattern: enhancedIdCard.has_subcity_pattern,
              status: enhancedIdCard.status,
              pattern_class: getSecurityPatternClass(enhancedIdCard, user)
            });
            setIdCard(enhancedIdCard);
          } catch (citizenError) {
            console.warn('⚠️ Could not fetch additional citizen details:', citizenError);
            // Still set the basic ID card data
            console.log('🔒 Basic ID card security pattern status:', {
              has_kebele_pattern: idCardData.has_kebele_pattern,
              has_subcity_pattern: idCardData.has_subcity_pattern,
              status: idCardData.status,
              pattern_class: getSecurityPatternClass(idCardData, user)
            });
            setIdCard(idCardData);
          }
        } else if (idCardData.citizen_details) {
          // Cross-tenant endpoint already provided citizen details
          console.log('✅ Using citizen details from cross-tenant endpoint');
          const enhancedIdCard = {
            ...idCardData,
            citizen: idCardData.citizen_details,
            tenant_hierarchy: {
              current_tenant: idCardData.kebele_tenant?.name || user?.tenant_name || 'ገብርኤል',
              parent_tenant: idCardData.kebele_tenant?.parent_name || user?.parent_tenant_name || 'ዞብል',
              city_tenant: user?.city_tenant_name || 'ጎንደር'
            }
          };
          console.log('🔒 Enhanced ID card (with cross-tenant citizen) security pattern status:', {
            has_kebele_pattern: enhancedIdCard.has_kebele_pattern,
            has_subcity_pattern: enhancedIdCard.has_subcity_pattern,
            status: enhancedIdCard.status,
            pattern_class: getSecurityPatternClass(enhancedIdCard, user)
          });
          setIdCard(enhancedIdCard);
        } else {
          // Add tenant hierarchy even for basic ID card data
          const basicIdCard = {
            ...idCardData,
            tenant_hierarchy: {
              current_tenant: user?.tenant_name || 'ገብርኤል',
              parent_tenant: user?.parent_tenant_name || 'ዞብል',
              city_tenant: user?.city_tenant_name || 'ጎንደር'
            }
          };
          console.log('🔒 Basic ID card (no citizen) security pattern status:', {
            has_kebele_pattern: basicIdCard.has_kebele_pattern,
            has_subcity_pattern: basicIdCard.has_subcity_pattern,
            status: basicIdCard.status,
            pattern_class: getSecurityPatternClass(basicIdCard, user)
          });
          setIdCard(basicIdCard);
        }

      } catch (error) {
        console.error('❌ Error fetching ID card:', error);
        setError(error.response?.data?.detail || error.message || 'Failed to fetch ID card');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchIDCard();
    }
  }, [id]);

  const [printPreviewOpen, setPrintPreviewOpen] = useState(false);
  const [cardSide, setCardSide] = useState('front');

  const handlePrintPreview = () => {
    // Open print preview modal
    setPrintPreviewOpen(true);
  };

  const handleApproveDialogOpen = () => {
    setApproveDialogOpen(true);
  };

  const handleApproveDialogClose = () => {
    setApproveDialogOpen(false);
  };

  const handleRejectDialogOpen = () => {
    setRejectDialogOpen(true);
  };

  const handleRejectDialogClose = () => {
    setRejectDialogOpen(false);
  };

  const handleSubmitForApproval = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Submitting ID card for approval:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email,
        userIsSuperuser: user?.is_superuser
      });

      await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'submit_for_approval',
        comment: 'Submitted for kebele leader approval'
      });

      // Refresh the ID card data
      window.location.reload();
    } catch (error) {
      console.error('Error submitting for approval:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to submit for approval';
      setError(`Failed to submit for approval: ${errorMessage}`);
    }
  };

  const handleKebeleApprove = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Kebele approving ID card:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      const response = await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'kebele_approve',
        comment: comment
      });

      console.log('✅ Kebele approval response:', response.data);

      setApproveDialogOpen(false);
      setComment('');

      // Update the ID card state with the response data
      if (response.data) {
        console.log('🔄 Updating ID card state with new data:', {
          old_has_kebele_pattern: idCard.has_kebele_pattern,
          new_has_kebele_pattern: response.data.has_kebele_pattern,
          old_status: idCard.status,
          new_status: response.data.status
        });
        setIdCard(response.data);
      } else {
        // Fallback: fetch fresh data
        console.log('🔄 No response data, fetching fresh ID card data...');
        fetchIDCard();
      }
    } catch (error) {
      console.error('Error approving ID card at kebele level:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to approve ID card';
      setError(`Failed to approve ID card: ${errorMessage}`);
    }
  };

  const handleSubcityApprove = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Subcity approving ID card:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      // Check authentication status
      const accessToken = localStorage.getItem('accessToken');
      if (accessToken) {
        try {
          const base64Url = accessToken.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
          const tokenData = JSON.parse(jsonPayload);
          console.log('🔍 Token data for subcity approval:', {
            role: tokenData.role,
            tenant_id: tokenData.tenant_id,
            user_id: tokenData.user_id,
            exp: new Date(tokenData.exp * 1000),
            isExpired: tokenData.exp < Math.floor(Date.now() / 1000)
          });
        } catch (e) {
          console.warn('Could not decode token:', e);
        }
      }

      const response = await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'subcity_approve',
        comment: comment
      });

      console.log('✅ Subcity approval response:', response.data);

      setApproveDialogOpen(false);
      setComment('');

      // Update the ID card state with the response data
      if (response.data) {
        console.log('🔄 Updating ID card state with new data:', {
          old_has_subcity_pattern: idCard.has_subcity_pattern,
          new_has_subcity_pattern: response.data.has_subcity_pattern,
          old_status: idCard.status,
          new_status: response.data.status
        });
        setIdCard(response.data);
      } else {
        // Fallback: fetch fresh data
        console.log('🔄 No response data, fetching fresh ID card data...');
        fetchIDCard();
      }
    } catch (error) {
      console.error('Error approving ID card at subcity level:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to approve ID card';
      setError(`Failed to approve ID card: ${errorMessage}`);
    }
  };

  const handleReject = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Rejecting ID card:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'reject',
        comment: comment
      });

      setRejectDialogOpen(false);
      setComment('');
      // Refresh the ID card data
      fetchIDCard();
    } catch (error) {
      console.error('Error rejecting ID card:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to reject ID card';
      setError(`Failed to reject ID card: ${errorMessage}`);
    }
  };

  const handleResetToDraft = async () => {
    try {
      const effectiveTenantId = tenantId || getTenantId();

      console.log('🔍 Resetting ID card to draft:', {
        idCardId: id,
        tenantId: effectiveTenantId,
        userRole: user?.role,
        userEmail: user?.email
      });

      await axios.post(`/api/tenants/${effectiveTenantId}/idcards/${id}/approval_action/`, {
        action: 'reset_to_draft',
        comment: 'Reset to draft for resubmission after addressing rejection feedback'
      });

      // Refresh the ID card data
      window.location.reload();
    } catch (error) {
      console.error('Error resetting ID card to draft:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });

      // Show more specific error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Failed to reset ID card to draft';
      setError(`Failed to reset ID card to draft: ${errorMessage}`);
    }
  };

  const getStatusChip = (status) => {
    switch (status) {
      case 'approved':
        return <Chip label="Fully Approved - Ready for Printing" color="success" />;
      case 'kebele_approved':
        return <Chip label="Kebele Approved - Pending Subcity Approval" color="warning" />;
      case 'pending_approval':
        return <Chip label="Pending Kebele Approval" color="warning" />;
      case 'rejected':
        return <Chip label="Rejected" color="error" />;
      case 'draft':
        return <Chip label="Draft" color="default" />;
      case 'printed':
        return <Chip label="Printed" color="info" />;
      case 'issued':
        return <Chip label="Issued" color="primary" />;
      default:
        return <Chip label={status} />;
    }
  };

  const getWorkflowSteps = () => {
    const steps = [
      { label: 'Draft', completed: true },
      { label: 'Kebele Approval', completed: ['kebele_approved', 'approved', 'printed', 'issued'].includes(idCard.status) },
      { label: 'Subcity Approval', completed: ['approved', 'printed', 'issued'].includes(idCard.status) },
      { label: 'Ready for Printing', completed: ['approved', 'printed', 'issued'].includes(idCard.status) },
      { label: 'Printed & Issued', completed: ['printed', 'issued'].includes(idCard.status) }
    ];

    return steps;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography variant="h5" color="error" gutterBottom>
          Error Loading ID Card
        </Typography>
        <Typography variant="body1">{error}</Typography>
        <Button
          variant="contained"
          startIcon={<BackIcon />}
          onClick={() => {
            // If we came from cross-tenant navigation, go back to appropriate list
            if (tenantId) {
              navigate('/idcards/all-kebeles');
            } else {
              navigate('/idcards');
            }
          }}
          sx={{ mt: 3 }}
        >
          Back to ID Cards
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={() => {
              // If we came from cross-tenant navigation, go back to appropriate list
              if (tenantId) {
                navigate('/idcards/all-kebeles');
              } else {
                navigate('/idcards');
              }
            }}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1">
            ID Card Details
          </Typography>
        </Box>
        <Box>
          {idCard.status === 'draft' && user?.role === 'clerk' && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmitForApproval}
              sx={{ mr: 1 }}
            >
              Send for Approval
            </Button>
          )}
          {idCard.status === 'pending_approval' && user?.role === 'kebele_leader' && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<ApproveIcon />}
                onClick={handleApproveDialogOpen}
                sx={{ mr: 1 }}
              >
                Kebele Approve
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<RejectIcon />}
                onClick={handleRejectDialogOpen}
                sx={{ mr: 1 }}
              >
                Reject
              </Button>
            </>
          )}
          {idCard.status === 'kebele_approved' && user?.role === 'subcity_admin' && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<ApproveIcon />}
                onClick={handleApproveDialogOpen}
                sx={{ mr: 1 }}
              >
                Subcity Approve
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<RejectIcon />}
                onClick={handleRejectDialogOpen}
                sx={{ mr: 1 }}
              >
                Reject
              </Button>
            </>
          )}
          {idCard.status === 'approved' && hasPermission('print_idcards') && (
            <Button
              variant="contained"
              color="secondary"
              startIcon={<PrintIcon />}
              onClick={handlePrintPreview}
            >
              Print Preview
            </Button>
          )}
          {idCard.status === 'rejected' && (user?.role === 'clerk' || user?.is_superuser) && (
            <Button
              variant="contained"
              color="warning"
              startIcon={<RefreshIcon />}
              onClick={handleResetToDraft}
              sx={{ mr: 1 }}
            >
              Reset to Draft
            </Button>
          )}
        </Box>
      </Box>

      {/* Workflow Stepper - Minimized */}
      <Paper sx={{ p: 2, mb: 2, boxShadow: 1 }}>
        <Typography variant="h6" gutterBottom sx={{ fontSize: '1.1rem', mb: 1 }}>
          ID Card Workflow
        </Typography>
        <Stepper alternativeLabel>
          {getWorkflowSteps().map((step, index) => (
            <Step key={index} active={step.completed}>
              <StepLabel>{step.label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      <Grid container spacing={3}>
        {/* ID Card Preview - Minimized Container */}
        <Grid item xs={12} md={8}>
          <Card sx={{ boxShadow: 1 }}>
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                <Typography variant="h6" sx={{ fontSize: '1.1rem' }}>
                  ID Card Preview
                </Typography>
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  <Button
                    variant={showFront ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setShowFront(true)}
                    sx={{ minWidth: 70, fontSize: '0.75rem', py: 0.5 }}
                  >
                    Front
                  </Button>
                  <Button
                    variant={!showFront ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setShowFront(false)}
                    sx={{ minWidth: 70, fontSize: '0.75rem', py: 0.5 }}
                  >
                    Back
                  </Button>
                </Box>
              </Box>
              <Divider sx={{ mb: 2 }} />



              {/* Professional ID Card Template - Front/Back */}
              <Box
                className={`security-pattern-container ${getSecurityPatternClass(idCard, user)}`}
                sx={{
                  width: '100%',
                  maxWidth: 520, // Increased width
                  height: 280, // Fixed height for consistent sizing
                  bgcolor: 'white',
                  border: '2px solid #e0e0e0',
                  borderRadius: 2,
                  p: 2,
                  position: 'relative',
                  background: showFront
                    ? 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)'
                    : 'linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%)',
                  mx: 'auto',
                  boxShadow: '0 4px 16px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease-in-out'
                }}
              >
                {showFront ? (
                  // FRONT SIDE
                  <>
                    {/* Ethiopian Government Header with Flag and Logo */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1.5 }}>
                      {/* Ethiopian Flag */}
                      <Box sx={{
                        width: 40,
                        height: 30,
                        background: 'linear-gradient(to bottom, #009639 33%, #FFCD00 33% 66%, #DA121A 66%)',
                        borderRadius: 1,
                        mr: 1,
                        flexShrink: 0
                      }} />

                      {/* Header Text in 3 Lines */}
                      <Box sx={{ flex: 1, textAlign: 'center', px: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 'bold',
                            color: 'black',
                            lineHeight: 1.1,
                            fontSize: '0.55rem',
                            mb: 0.2
                          }}
                        >
                          በኢትዮጵያ ፌደራላዊ ዲሞክራሲያዊ ሪፐብሊክ በአማራ ብሔራዊ ክልላዊ መንግሥት
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 'bold',
                            color: 'black',
                            lineHeight: 1.1,
                            fontSize: '0.55rem',
                            mb: 0.2
                          }}
                        >
                          በጎንደር ከተማ አስተዳደር በዞብል ክ/ከተማ
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 'bold',
                            color: 'black',
                            lineHeight: 1.1,
                            fontSize: '0.55rem'
                          }}
                        >
                          የገብርኤል ቀበሌ አስተዳደር ጽ/ቤት የነዋሪዎች መታወቂያ ካርድ
                        </Typography>
                      </Box>

                      {/* National ID Logo */}
                      <Box sx={{
                        width: 30,
                        height: 30,
                        bgcolor: 'primary.main',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        ml: 1,
                        flexShrink: 0
                      }}>
                        <Typography variant="caption" sx={{ color: 'white', fontWeight: 'bold', fontSize: '0.6rem' }}>
                          ID
                        </Typography>
                      </Box>
                    </Box>

                    {/* Main Content - Enhanced */}
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      {/* Photo Section - With Real Photo and ID Number */}
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Box sx={{
                          width: 90,
                          height: 110,
                          border: '1px solid #ccc',
                          borderRadius: 1,
                          overflow: 'hidden',
                          position: 'relative',
                          mb: 1
                        }}>
                          {idCard?.citizen_photo ? (
                            <img
                              src={idCard.citizen_photo}
                              alt="Citizen Photo"
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover'
                              }}
                            />
                          ) : (
                            <Box sx={{
                              width: '100%',
                              height: '100%',
                              bgcolor: 'grey.200',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                PHOTO
                              </Typography>
                            </Box>
                          )}
                        </Box>

                        {/* ID Number under photo */}
                        <Typography variant="body2" sx={{
                          fontWeight: 'bold',
                          fontSize: '0.7rem',
                          color: 'error.main',
                          textAlign: 'center',
                          fontFamily: 'monospace'
                        }}>
                          {idCard?.citizen_digital_id || 'DIGITAL-ID'}
                        </Typography>
                      </Box>

                      {/* Details Section - Ethiopian Style */}
                      <Box sx={{ flex: 1 }}>
                        {/* Name and Gender Section */}
                        <Box sx={{ display: 'flex', gap: 2, mb: 0.5 }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.6rem', fontWeight: 'bold' }}>
                              ሙሉ ስም / First, Middle, Surname
                            </Typography>
                            {/* Amharic Name */}
                            {(idCard?.citizen_first_name_am || idCard?.citizen_middle_name_am || idCard?.citizen_last_name_am) && (
                              <Typography variant="caption" sx={{ display: 'block', fontSize: '0.6rem', color: 'text.secondary', mb: 0.2 }}>
                                {[idCard?.citizen_first_name_am, idCard?.citizen_middle_name_am, idCard?.citizen_last_name_am]
                                  .filter(Boolean)
                                  .join(' ') || 'ሙ-ስም ሙ-ስም ሙ-ስም'}
                              </Typography>
                            )}
                            {/* English Name */}
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.8rem', lineHeight: 1.2 }}>
                              {idCard?.citizen_name ||
                               [idCard?.citizen_first_name, idCard?.citizen_middle_name, idCard?.citizen_last_name]
                                 .filter(Boolean)
                                 .join(' ') ||
                               'Test Test Test'}
                            </Typography>
                          </Box>

                          {/* Gender and Citizenship Section */}
                          <Box>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.6rem', fontWeight: 'bold' }}>
                              ፆታ / SEX
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>
                              {idCard?.citizen_gender === 'female' ? 'ሴት' : 'ወንድ'}
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.7rem', mb: 0.5 }}>
                              {idCard?.citizen_gender ? (idCard.citizen_gender.charAt(0).toUpperCase() + idCard.citizen_gender.slice(1)) : 'Male'}
                            </Typography>

                            {/* Citizenship underneath gender */}
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.6rem', fontWeight: 'bold' }}>
                              ዜግነት / Country of Citizenship
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>
                              ኢት / ET
                            </Typography>
                          </Box>
                        </Box>

                        {/* Date Fields underneath name */}
                        <Box sx={{ display: 'flex', gap: 2, mb: 1 }}>
                          {/* Expiry Date - First */}
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.6rem', fontWeight: 'bold' }}>
                              ጊዜው የሚያልቅበት ዕለት / Date of Expiry
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>
                              {idCard?.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString('en-GB') : '27/05/2030'}
                            </Typography>
                          </Box>

                          {/* Date of Birth - Second */}
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.6rem', fontWeight: 'bold' }}>
                              የተወለደ ዕለት / Date of Birth
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}>
                              {idCard?.citizen_date_of_birth ? new Date(idCard.citizen_date_of_birth).toLocaleDateString('en-GB') : '26/05/2001'}
                            </Typography>
                          </Box>
                        </Box>


                      </Box>
                    </Box>

                  </>
                ) : (
                  // BACK SIDE
                  <>
                    {/* Back Header */}
                    <Box sx={{ textAlign: 'center', mb: 1.5 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 0.3, fontSize: '0.7rem' }}>
                        NATIONAL IDENTITY CARD
                      </Typography>
                      <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.6rem' }}>
                        Federal Democratic Republic of Ethiopia
                      </Typography>
                    </Box>

                    {/* Back Content */}
                    <Box sx={{ display: 'flex', gap: 2, height: '60%' }}>
                      {/* Left Side - Additional Info */}
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1, color: 'primary.main', fontSize: '0.7rem' }}>
                          ADDITIONAL INFORMATION
                        </Typography>

                        <Box sx={{ display: 'grid', gap: 1 }}>
                          <Box>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontWeight: 'bold', fontSize: '0.55rem' }}>
                              DATE OF BIRTH
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.65rem' }}>
                              {idCard?.citizen_date_of_birth ? new Date(idCard.citizen_date_of_birth).toLocaleDateString('en-GB') : 'DD/MM/YYYY'}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontWeight: 'bold', fontSize: '0.55rem' }}>
                              PLACE OF BIRTH
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.65rem' }}>
                              {idCard?.citizen_place_of_birth || 'ETHIOPIA'}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontWeight: 'bold', fontSize: '0.55rem' }}>
                              VERIFICATION CODE
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', fontFamily: 'monospace', fontSize: '0.65rem' }}>
                              {idCard?.citizen_digital_id || idCard?.card_number || 'XXXXXXXX'}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      {/* Right Side - QR Code */}
                      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold', mb: 1, color: 'primary.main', fontSize: '0.6rem' }}>
                          VERIFICATION QR CODE
                        </Typography>

                        {/* Smaller QR Code */}
                        <Box sx={{ mb: 0.5 }}>
                          <QRCode
                            value={`https://goid.gov.et/verify/${idCard?.citizen_digital_id || 'UNKNOWN'}`}
                            size={70}
                          />
                          {/* Fallback placeholder if QR fails to load */}
                          <Box sx={{
                            width: 70,
                            height: 70,
                            bgcolor: 'grey.300',
                            display: 'none',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.8rem',
                            fontWeight: 'bold',
                            borderRadius: 1,
                            border: '2px solid #ccc'
                          }}>
                            QR
                          </Box>
                        </Box>

                        <Typography variant="caption" sx={{ color: 'text.secondary', fontWeight: 'bold', textAlign: 'center', fontSize: '0.55rem' }}>
                          Scan to verify citizen identity
                        </Typography>
                      </Box>
                    </Box>

                    {/* Back Footer */}
                    <Box sx={{
                      position: 'absolute',
                      bottom: 8,
                      left: 8,
                      right: 8,
                      textAlign: 'center'
                    }}>
                      <Typography variant="caption" sx={{ color: 'text.secondary', fontStyle: 'italic', fontSize: '0.55rem' }}>
                        This card is the property of the Federal Democratic Republic of Ethiopia.
                        If found, please return to the nearest government office.
                      </Typography>
                    </Box>
                  </>
                )}

                {/* Security Pattern Status Indicator - Removed for cleaner design */}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* ID Card Details - Minimized Container */}
        <Grid item xs={12} md={4}>
          <Card sx={{ boxShadow: 1 }}>
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Typography variant="h6" gutterBottom sx={{ fontSize: '1.1rem', mb: 1 }}>
                ID Card Information
              </Typography>
              <Divider sx={{ mb: 1.5 }} />

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                  <Box sx={{ mb: 1 }}>
                    {getStatusChip(idCard.status)}
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Issue Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.issue_date ? new Date(idCard.issue_date).toLocaleDateString() : 'Not issued yet'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Expiry Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString() : 'Not set'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Citizen Information
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Full Name
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen_name || 'Unknown Citizen'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Digital ID
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.citizen_digital_id || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Card Status
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    <Box component="span" sx={{ textTransform: 'capitalize' }}>
                      {idCard.status || 'Unknown'}
                    </Box>
                  </Typography>
                </Grid>

                {/* Security Pattern Information */}
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    🔒 Security Pattern Status
                  </Typography>
                  <Box sx={{
                    p: 1.5,
                    mt: 1,
                    bgcolor: (idCard.has_kebele_pattern || idCard.has_subcity_pattern) ? 'success.light' : 'grey.100',
                    borderRadius: 1,
                    border: '2px solid',
                    borderColor: (idCard.has_kebele_pattern || idCard.has_subcity_pattern) ? 'success.main' : 'grey.300'
                  }}>
                    <Typography variant="body1" fontWeight="bold" gutterBottom>
                      {getPatternDescription(idCard, user)}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {idCard.has_kebele_pattern && (
                        <Chip
                          label="✅ Kebele Pattern"
                          color="warning"
                          size="small"
                          sx={{ fontWeight: 'bold' }}
                        />
                      )}
                      {idCard.has_subcity_pattern && (
                        <Chip
                          label="✅ Subcity Pattern"
                          color="success"
                          size="small"
                          sx={{ fontWeight: 'bold' }}
                        />
                      )}
                      {!idCard.has_kebele_pattern && !idCard.has_subcity_pattern && (
                        <Chip
                          label="⏳ No Pattern Applied"
                          color="default"
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </Box>
                </Grid>

                {/* Approval Information */}
                {idCard.status === 'approved' && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Approved By
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {idCard.approved_by_username || 'Unknown'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Approved Date
                      </Typography>
                      <Typography variant="body1" gutterBottom>
                        {idCard.approved_at ? new Date(idCard.approved_at).toLocaleDateString() : 'Unknown'}
                      </Typography>
                    </Grid>
                    {idCard.approval_pattern && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Approval Pattern
                        </Typography>
                        <Typography variant="body1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                          {idCard.approval_pattern.replace('_', ' ')}
                        </Typography>
                      </Grid>
                    )}
                    {idCard.approval_comment && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Approval Comment
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {idCard.approval_comment}
                        </Typography>
                      </Grid>
                    )}
                  </>
                )}

                {idCard.status === 'rejected' && idCard.approval_comment && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Rejection Reason
                    </Typography>
                    <Typography variant="body1" gutterBottom color="error">
                      {idCard.approval_comment}
                    </Typography>
                  </Grid>
                )}
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    System Information
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Created By
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.created_by_username || 'System'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Created Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.created_at ? new Date(idCard.created_at).toLocaleDateString() : 'Unknown'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Issue Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.issue_date ? new Date(idCard.issue_date).toLocaleDateString() : 'Not issued yet'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Expiry Date
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {idCard.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString() : 'Not set'}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Approve Dialog */}
      <Dialog open={approveDialogOpen} onClose={handleApproveDialogClose}>
        <DialogTitle>
          {idCard.status === 'pending_approval' ? 'Kebele Approval' : 'Subcity Approval'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {idCard.status === 'pending_approval'
              ? 'Are you sure you want to approve this ID card at kebele level? This will apply the first security pattern and send it to subcity for final approval.'
              : 'Are you sure you want to give final approval to this ID card? This will complete the security pattern and make it ready for printing.'
            }
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="comment"
            label="Comment (Optional)"
            type="text"
            fullWidth
            variant="outlined"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleApproveDialogClose}>Cancel</Button>
          <Button
            onClick={idCard.status === 'pending_approval' ? handleKebeleApprove : handleSubcityApprove}
            variant="contained"
            color="success"
          >
            {idCard.status === 'pending_approval' ? 'Kebele Approve' : 'Subcity Approve'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onClose={handleRejectDialogClose}>
        <DialogTitle>Reject ID Card</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to reject this ID card? Please provide a reason for rejection.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="comment"
            label="Reason for Rejection"
            type="text"
            fullWidth
            variant="outlined"
            required
            value={comment}
            onChange={(e) => setComment(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRejectDialogClose}>Cancel</Button>
          <Button onClick={handleReject} variant="contained" color="error" disabled={!comment}>
            Reject
          </Button>
        </DialogActions>
      </Dialog>

      {/* Print Preview Modal */}
      <Dialog
        open={printPreviewOpen}
        onClose={() => setPrintPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Print Preview - {idCard?.citizen_name || idCard?.citizen?.first_name + ' ' + idCard?.citizen?.last_name || 'ID Card'}
        </DialogTitle>
        <DialogContent>
          {/* Card Side Toggle */}
          <Box display="flex" justifyContent="center" mb={3}>
            <ToggleButtonGroup
              value={cardSide}
              exclusive
              onChange={(event, newSide) => {
                if (newSide !== null) {
                  setCardSide(newSide);
                }
              }}
              aria-label="card side"
            >
              <ToggleButton value="front" aria-label="front side">
                <FrontIcon sx={{ mr: 1 }} />
                Front Side
              </ToggleButton>
              <ToggleButton value="back" aria-label="back side">
                <BackSideIcon sx={{ mr: 1 }} />
                Back Side
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>

          {/* ID Card Preview */}
          <Box display="flex" justifyContent="center" mb={3}>
            <Paper
              elevation={3}
              sx={{
                p: 2,
                backgroundColor: '#f5f5f5',
                maxWidth: '600px',
                width: '100%'
              }}
            >
              {idCard && (
                <Box
                  className={`security-pattern-container ${getSecurityPatternClass(idCard, user)}`}
                  sx={{
                    width: '100%',
                    height: '350px',
                    position: 'relative',
                    backgroundColor: '#ffffff',
                    border: '2px solid #1976d2',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    fontFamily: 'Arial, sans-serif',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                  }}
                >
                  {cardSide === 'front' ? (
                    // Front Side
                    <>
                      {/* Header */}
                      <Box
                        sx={{
                          backgroundColor: '#1976d2',
                          color: 'white',
                          p: 1,
                          textAlign: 'center'
                        }}
                      >
                        <Typography variant="h6" sx={{ fontSize: '14px', fontWeight: 'bold' }}>
                          የኢትዮጵያ ፌዴራላዊ ዲሞክራሲያዊ ሪፐብሊክ
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '12px' }}>
                          Federal Democratic Republic of Ethiopia
                        </Typography>
                        <Typography variant="body1" sx={{ fontSize: '13px', fontWeight: 'bold' }}>
                          {idCard.tenant_hierarchy?.city_tenant || 'ጎንደር'} ከተማ - {idCard.tenant_hierarchy?.parent_tenant || 'ዞብል'} ክፍለ ከተማ - {idCard.tenant_hierarchy?.current_tenant || 'ገብርኤል'} ቀበሌ
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '11px' }}>
                          Digital Identity Card
                        </Typography>
                      </Box>

                      {/* Content */}
                      <Box sx={{ p: 2, display: 'flex' }}>
                        {/* Photo */}
                        <Box sx={{ mr: 2 }}>
                          <Box
                            sx={{
                              width: 80,
                              height: 100,
                              border: '2px solid #1976d2',
                              borderRadius: 1,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              backgroundColor: '#f5f5f5',
                              backgroundImage: (idCard.citizen_photo || idCard.citizen?.photo) ? `url(${idCard.citizen_photo || idCard.citizen?.photo})` : 'none',
                              backgroundSize: 'cover',
                              backgroundPosition: 'center'
                            }}
                          >
                            {!(idCard.citizen_photo || idCard.citizen?.photo) && (
                              <Typography variant="caption" sx={{ fontSize: '10px', textAlign: 'center' }}>
                                PHOTO
                              </Typography>
                            )}
                          </Box>

                          {/* ID Number under photo */}
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: '11px',
                              fontWeight: 'bold',
                              textAlign: 'center',
                              mt: 1,
                              fontFamily: 'monospace'
                            }}
                          >
                            {idCard.card_number || 'ID-000000'}
                          </Typography>
                        </Box>

                        {/* Details */}
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6" sx={{ fontSize: '14px', fontWeight: 'bold', mb: 1 }}>
                            {idCard.citizen_name ||
                             [idCard.citizen_first_name, idCard.citizen_middle_name, idCard.citizen_last_name]
                               .filter(Boolean).join(' ') ||
                             (idCard.citizen && [idCard.citizen.first_name, idCard.citizen.middle_name, idCard.citizen.last_name]
                               .filter(Boolean).join(' ')) || 'N/A'}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Typography variant="body2" sx={{ fontSize: '11px', fontWeight: 'bold', mr: 1 }}>
                              ጾታ/Gender:
                            </Typography>
                            <Typography variant="body2" sx={{ fontSize: '11px' }}>
                              {(idCard.citizen_gender || idCard.citizen?.gender) === 'M' || (idCard.citizen_gender || idCard.citizen?.gender) === 'male' ? 'ወንድ/Male' :
                               (idCard.citizen_gender || idCard.citizen?.gender) === 'F' || (idCard.citizen_gender || idCard.citizen?.gender) === 'female' ? 'ሴት/Female' : 'N/A'}
                            </Typography>
                          </Box>

                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 0.5 }}>
                            <strong>የትውልድ ቀን/DOB:</strong> {idCard.citizen_date_of_birth || idCard.citizen?.date_of_birth || 'N/A'}
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 0.5 }}>
                            <strong>ዲጂታል መታወቂያ/Digital ID:</strong> {idCard.citizen_digital_id || idCard.citizen?.digital_id || idCard.card_number || 'N/A'}
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 0.5 }}>
                            <strong>የመኖሪያ አድራሻ/Address:</strong> {idCard.citizen_address || idCard.citizen?.address || 'N/A'}
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '10px' }}>
                            <strong>የስልክ ቁጥር/Phone:</strong> {idCard.citizen_phone_number || idCard.citizen?.phone_number || 'N/A'}
                          </Typography>
                        </Box>
                      </Box>

                      {/* Footer */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          backgroundColor: '#f5f5f5',
                          p: 0.5,
                          textAlign: 'center'
                        }}
                      >
                        <Typography variant="caption" sx={{ fontSize: '9px' }}>
                          Issue Date: {idCard.issue_date || 'N/A'} | Expires: {idCard.expiry_date || 'N/A'}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    // Back Side
                    <>
                      {/* Header */}
                      <Box
                        sx={{
                          backgroundColor: '#1976d2',
                          color: 'white',
                          p: 1,
                          textAlign: 'center'
                        }}
                      >
                        <Typography variant="h6" sx={{ fontSize: '14px', fontWeight: 'bold' }}>
                          Digital Identity Card - Back
                        </Typography>
                      </Box>

                      {/* Content */}
                      <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between' }}>
                        {/* Left side - Additional info */}
                        <Box sx={{ flex: 1, mr: 2 }}>
                          <Typography variant="body2" sx={{ fontSize: '11px', mb: 1, fontWeight: 'bold' }}>
                            Additional Information:
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 0.5 }}>
                            <strong>Blood Type:</strong> {idCard.citizen_blood_type || idCard.citizen?.blood_type || 'N/A'}
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 0.5 }}>
                            <strong>Place of Birth:</strong> {idCard.citizen_place_of_birth || idCard.citizen?.place_of_birth || idCard.citizen?.subcity || idCard.citizen?.kebele || 'N/A'}
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 0.5 }}>
                            <strong>Nationality:</strong> Ethiopian
                          </Typography>

                          <Typography variant="body2" sx={{ fontSize: '9px', fontStyle: 'italic', mt: 2 }}>
                            This card is property of the Federal Democratic Republic of Ethiopia.
                            If found, please return to the nearest government office.
                          </Typography>
                        </Box>

                        {/* Right side - QR Code */}
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                          <Typography variant="body2" sx={{ fontSize: '10px', mb: 1, fontWeight: 'bold' }}>
                            QR Code
                          </Typography>
                          <QRCode
                            value={idCard.citizen_digital_id || idCard.card_number || 'ID-000000'}
                            size={80}
                          />
                          <Typography variant="caption" sx={{ fontSize: '8px', mt: 0.5, textAlign: 'center' }}>
                            Scan for verification
                          </Typography>
                        </Box>
                      </Box>

                      {/* Footer */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          backgroundColor: '#f5f5f5',
                          p: 0.5,
                          textAlign: 'center'
                        }}
                      >
                        <Typography variant="caption" sx={{ fontSize: '9px' }}>
                          Card ID: {idCard.uuid || 'N/A'} | Status: {idCard.status || 'N/A'}
                        </Typography>
                      </Box>
                    </>
                  )}
                </Box>
              )}
            </Paper>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPrintPreviewOpen(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PrintIcon />}
            onClick={() => {
              // Here you can add actual print functionality
              window.print();
              setPrintPreviewOpen(false);
            }}
          >
            Print ID Card
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IDCardView;

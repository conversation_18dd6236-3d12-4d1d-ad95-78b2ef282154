from django.utils import timezone
import datetime
from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException
from django.db import connection


def calculate_age(birth_date):
    """Calculate age from birth date."""
    today = timezone.now().date()
    return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))


def generate_expiry_date(issue_date=None, years=5):
    """Generate expiry date based on issue date."""
    if not issue_date:
        issue_date = timezone.now().date()
    return issue_date + datetime.timedelta(days=years*365)


def format_phone_number(phone_number):
    """Format phone number to standard format."""
    if not phone_number:
        return None

    # Remove any non-digit characters
    digits = ''.join(filter(str.isdigit, phone_number))

    # Add country code if missing
    if len(digits) == 9 and not digits.startswith('251'):
        digits = '251' + digits
    elif len(digits) == 10 and digits.startswith('0'):
        digits = '251' + digits[1:]

    # Format with plus sign
    if not digits.startswith('+'):
        digits = '+' + digits

    return digits


def custom_exception_handler(exc, context):
    """Custom exception handler that includes tenant information."""
    response = exception_handler(exc, context)

    if response is not None:
        # Add tenant information to the response
        if hasattr(connection, 'tenant') and connection.tenant:
            # Handle both real Tenant objects and FakeTenant objects
            tenant_info = {}

            # Safely get tenant attributes
            if hasattr(connection.tenant, 'id'):
                tenant_info['id'] = connection.tenant.id
            else:
                tenant_info['id'] = 'fake_tenant'

            if hasattr(connection.tenant, 'name'):
                tenant_info['name'] = connection.tenant.name
            else:
                tenant_info['name'] = 'FakeTenant'

            if hasattr(connection.tenant, 'schema_name'):
                tenant_info['schema_name'] = connection.tenant.schema_name
            else:
                tenant_info['schema_name'] = 'public'

            response.data['tenant'] = tenant_info

        # Add more detailed error information
        if isinstance(exc, APIException):
            response.data['status_code'] = response.status_code
            response.data['exception'] = exc.__class__.__name__

    return response

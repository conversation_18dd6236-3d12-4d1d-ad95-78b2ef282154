# Generated by Django 4.2.7 on 2025-05-22 21:47

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=3, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Countries',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Subcity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('subcity_code', models.CharField(max_length=10, unique=True)),
                ('subcity_name', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/subcity/')),
                ('motto_slogan', models.TextField(blank=True, null=True)),
                ('subcity_intro', models.TextField(blank=True, null=True)),
                ('administrator_name', models.CharField(blank=True, max_length=100, null=True)),
                ('deputy_administrator', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('google_maps_url', models.URLField(blank=True, null=True)),
                ('area_sq_km', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('population', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('website', models.URLField(blank=True, null=True)),
                ('office_address', models.TextField(blank=True, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('established_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_subcities', to=settings.AUTH_USER_MODEL)),
                ('tenant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='subcity_profile', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'Subcity Administration',
                'verbose_name_plural': 'Subcity Administrations',
                'ordering': ['subcity_name', 'subcity_code'],
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='tenants.country')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Kebele',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('kebele_code', models.CharField(max_length=10, unique=True)),
                ('kebele_name', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/kebele/')),
                ('kebele_intro', models.TextField(blank=True, null=True)),
                ('chairman_name', models.CharField(blank=True, max_length=100, null=True)),
                ('manager_name', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('google_maps_url', models.URLField(blank=True, null=True)),
                ('area_sq_km', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('population', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('office_address', models.TextField(blank=True, null=True)),
                ('established_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_kebeles', to=settings.AUTH_USER_MODEL)),
                ('tenant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='kebele_profile', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'Kebele Administration',
                'verbose_name_plural': 'Kebele Administrations',
                'ordering': ['kebele_name', 'kebele_code'],
            },
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('city_code', models.CharField(max_length=10, unique=True)),
                ('city_name', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/')),
                ('motto_slogan', models.TextField(blank=True, null=True)),
                ('city_intro', models.TextField(blank=True, null=True)),
                ('mayor_name', models.CharField(blank=True, max_length=100, null=True)),
                ('deputy_mayor', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('google_maps_url', models.URLField(blank=True, null=True)),
                ('area_sq_km', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('elevation_meters', models.IntegerField(blank=True, null=True)),
                ('timezone', models.CharField(default='EAT', max_length=50)),
                ('website', models.URLField(blank=True, null=True)),
                ('headquarter_address', models.TextField(blank=True, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('established_date', models.DateField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.country')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_cities', to=settings.AUTH_USER_MODEL)),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cities', to='tenants.region')),
                ('tenant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='city_profile', to='tenants.tenant')),
            ],
            options={
                'verbose_name': 'City Administration',
                'verbose_name_plural': 'City Administrations',
                'ordering': ['city_name', 'city_code'],
            },
        ),
    ]

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Typography,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  CircularProgress,
  useTheme,
  alpha,
  Fade,
  Zoom,
  Alert,
} from '@mui/material';
import {
  Person as PersonIcon,
  CreditCard as CardIcon,
  CheckCircle as ApprovedIcon,
  Pending as PendingIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  VerifiedUser as VerifiedUserIcon,
  HourglassEmpty as PendingRequestsIcon,
  Groups as GroupsIcon,
  Print as PrintIcon,
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as ChartTooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import StyledCard from '../../components/common/StyledCard';
import DataCard from '../../components/common/DataCard';



const Dashboard = () => {
  const { user, getTenantId } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Dashboard data state
  const [stats, setStats] = useState({
    totalCitizens: 0,
    totalIdCards: 0,
    pendingApprovals: 0,
    approvedCards: 0,
    printingQueue: 0,
    maleCount: 0,
    femaleCount: 0,
    newRegistrationsThisMonth: 0,
    expiringIds: 0,
    expiredIds: 0,
    flaggedCases: 0,
  });
  const [populationByKetena, setPopulationByKetena] = useState([]);
  const [ageGroupDistribution, setAgeGroupDistribution] = useState([]);
  const [recentCitizens, setRecentCitizens] = useState([]);
  const [recentIdCards, setRecentIdCards] = useState([]);
  const [monthlyStats, setMonthlyStats] = useState([]);
  const [statusDistribution, setStatusDistribution] = useState([]);
  const [migrationData, setMigrationData] = useState({ into: 0, outOf: 0 });
  const [topKetenas, setTopKetenas] = useState([]);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get tenant ID from auth context
        const tenantId = getTenantId();
        console.log('🔍 Dashboard Debug - User:', user);
        console.log('🔍 Dashboard Debug - User tenant_id property:', user?.tenant_id);
        console.log('🔍 Dashboard Debug - getTenantId() result:', tenantId);
        console.log('🔍 Dashboard Debug - User Role:', user?.role);
        console.log('🔍 Dashboard Debug - Is Superuser:', user?.is_superuser);
        console.log('🔍 Dashboard Debug - User keys:', user ? Object.keys(user) : 'No user');
        console.log('🔍 Dashboard Debug - Full user object:', user);

        // Try to get tenant ID from user object directly if getTenantId fails
        let actualTenantId = tenantId || user?.tenant_id;

        // If still no tenant ID, try to extract directly from JWT token
        if (!actualTenantId) {
          try {
            const token = localStorage.getItem('accessToken');
            if (token) {
              const payload = JSON.parse(atob(token.split('.')[1]));
              actualTenantId = payload.tenant_id;
              console.log('🔍 Dashboard Debug - Extracted tenant_id from JWT:', actualTenantId);
              console.log('🔍 Dashboard Debug - Full JWT payload:', payload);
            }
          } catch (error) {
            console.error('🔍 Dashboard Debug - Error extracting tenant_id from JWT:', error);
          }
        }

        console.log('🔍 Dashboard Debug - Final tenant ID to use:', actualTenantId);

        // Ensure we have a tenant ID
        if (!actualTenantId) {
          throw new Error('No tenant ID found. Please log in again.');
        }

        // Fetch all data in parallel
        console.log('🔍 Dashboard Debug - Making API calls for tenant:', actualTenantId);

        // Make API calls with detailed logging
        console.log('🔍 Making comprehensive dashboard API calls...');

        const [
          citizenStatsResponse,
          idCardStatsResponse,
          recentCitizensResponse,
          recentIdCardsResponse,
          dashboardReportsResponse,
        ] = await Promise.all([
          axios.get(`/api/tenants/${actualTenantId}/citizens/stats/`),
          axios.get(`/api/tenants/${actualTenantId}/idcards/stats/`),
          axios.get(`/api/tenants/${actualTenantId}/citizens/?limit=5&ordering=-created_at`),
          axios.get(`/api/tenants/${actualTenantId}/idcards/?limit=5&ordering=-created_at`),
          axios.get(`/api/tenants/${actualTenantId}/dashboard/reports/`).catch(err => {
            console.warn('Dashboard reports endpoint not available, using basic data');
            return { data: {} };
          }),
        ]);

        console.log('✅ All API responses received:', {
          citizenStats: citizenStatsResponse.data,
          idCardStats: idCardStatsResponse.data,
          recentCitizens: recentCitizensResponse.data,
          recentIdCards: recentIdCardsResponse.data,
          dashboardReports: dashboardReportsResponse.data,
        });

        console.log('🔍 Dashboard Debug - All API responses received successfully');

        // Process citizen statistics
        const citizenStats = citizenStatsResponse.data;
        const idCardStats = idCardStatsResponse.data;
        const dashboardReports = dashboardReportsResponse.data;

        console.log('🔍 Processing enhanced dashboard data...');

        // Calculate derived statistics
        const pendingApprovals = idCardStats.status_distribution?.pending_approval || 0;
        const approvedCards = idCardStats.status_distribution?.approved || 0;
        const printingQueue = idCardStats.status_distribution?.printing || 0;

        // Enhanced stats with reports data
        const enhancedStats = {
          totalCitizens: citizenStats.total || 0,
          totalIdCards: idCardStats.total_id_cards || 0,
          pendingApprovals,
          approvedCards,
          printingQueue,
          maleCount: citizenStats.male || 0,
          femaleCount: citizenStats.female || 0,
          newRegistrationsThisMonth: dashboardReports.new_registrations_this_month || 0,
          expiringIds: dashboardReports.expiring_ids_next_30_days || 0,
          expiredIds: dashboardReports.expired_ids_over_30_days || 0,
          flaggedCases: dashboardReports.flagged_cases || 0,
        };

        setStats(enhancedStats);
        console.log('✅ Enhanced stats updated:', enhancedStats);

        // Process ketena-level data
        if (dashboardReports.population_by_ketena) {
          setPopulationByKetena(dashboardReports.population_by_ketena);
          setTopKetenas(dashboardReports.population_by_ketena.slice(0, 3));
        }

        // Process age group distribution
        if (dashboardReports.age_group_distribution) {
          setAgeGroupDistribution(dashboardReports.age_group_distribution);
        }

        // Process migration data
        if (dashboardReports.migration_data) {
          setMigrationData(dashboardReports.migration_data);
        }

        // Process recent citizens
        const citizens = recentCitizensResponse.data.results || recentCitizensResponse.data || [];
        console.log('🔍 Processing recent citizens:', citizens);
        setRecentCitizens(citizens.slice(0, 5));
        console.log('✅ Recent citizens updated:', citizens.slice(0, 5));

        // Process recent ID cards
        const idCards = recentIdCardsResponse.data.results || recentIdCardsResponse.data || [];
        console.log('🔍 Processing recent ID cards:', idCards);
        setRecentIdCards(idCards.slice(0, 5));
        console.log('✅ Recent ID cards updated:', idCards.slice(0, 5));

        // Process status distribution for pie chart
        if (idCardStats.status_distribution) {
          const statusData = Object.entries(idCardStats.status_distribution)
            .filter(([_, count]) => count > 0)
            .map(([status, count]) => ({
              name: status.replace('_', ' ').toUpperCase(),
              value: count,
              status: status
            }));
          console.log('🔍 Processing status distribution:', statusData);
          setStatusDistribution(statusData);
          console.log('✅ Status distribution updated:', statusData);
        } else {
          console.log('⚠️ No status distribution data found');
        }

        // Process monthly statistics
        if (idCardStats.monthly_statistics) {
          const monthlyData = idCardStats.monthly_statistics.map(item => ({
            name: new Date(item.month).toLocaleDateString('en-US', { month: 'short' }),
            idCards: item.count,
            citizens: 0, // We'll need to add this to the backend if needed
          }));
          console.log('🔍 Processing monthly stats:', monthlyData);
          setMonthlyStats(monthlyData);
          console.log('✅ Monthly stats updated:', monthlyData);
        } else {
          console.log('⚠️ No monthly statistics data found');
        }

        console.log('🎉 Dashboard data processing completed successfully!');

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError(error.response?.data?.detail || error.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    // Only fetch data if user is loaded
    if (user) {
      fetchDashboardData();
    }
  }, [user, getTenantId]);

  const theme = useTheme();

  // Show loading if user is not loaded yet or if we're fetching data
  if (!user || loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
          {!user ? 'Loading user data...' : 'Loading dashboard data...'}
        </Typography>
        {/* Debug info */}
        <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
          User: {user ? 'Loaded' : 'Not loaded'}, Loading: {loading ? 'Yes' : 'No'}
        </Typography>
        {user && (
          <>
            <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
              Username: {user.username}, Tenant ID: {user.tenant_id || 'None'}
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
              Role: {user.role}, Tenant Name: {user.tenant_name || 'None'}
            </Typography>
          </>
        )}
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="contained"
          onClick={() => window.location.reload()}
          sx={{ mt: 2 }}
        >
          Retry
        </Button>
      </Box>
    );
  }

  // Note: We'll show the dashboard even without tenant_id, using mock data

  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
          Welcome back, {user?.username || 'User'}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Here's what's happening with your ID management system today
        </Typography>

        {/* Debug Information */}
        <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
          <Typography variant="body2" color="text.secondary">
            🔍 Debug Info - Tenant: {user?.tenant_name || 'None'} (ID: {user?.tenant_id || 'None'}),
            Role: {user?.role || 'None'}, Stats: Citizens={stats.totalCitizens}, Cards={stats.totalIdCards}
          </Typography>
        </Box>
      </Box>

      {/* Primary Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '100ms' }}>
            <Box>
              <DataCard
                title="Total Registered Citizens"
                value={stats.totalCitizens}
                subtitle={`${stats.maleCount} male, ${stats.femaleCount} female`}
                icon={<PersonIcon />}
                color="primary"
                onClick={() => navigate('/citizens')}
              />
            </Box>
          </Fade>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '200ms' }}>
            <Box>
              <DataCard
                title="New Registrations"
                value={stats.newRegistrationsThisMonth}
                subtitle="This month"
                icon={<TrendingUpIcon />}
                color="success"
                onClick={() => navigate('/citizens?filter=new')}
              />
            </Box>
          </Fade>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '300ms' }}>
            <Box>
              <DataCard
                title="Pending Approvals"
                value={stats.pendingApprovals}
                subtitle="Awaiting review"
                icon={<PendingRequestsIcon />}
                color="warning"
                onClick={() => navigate('/idcards?status=pending_approval')}
              />
            </Box>
          </Fade>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '400ms' }}>
            <Box>
              <DataCard
                title="Flagged Cases"
                value={stats.flaggedCases}
                subtitle="Require attention"
                icon={<VerifiedUserIcon />}
                color="error"
                onClick={() => navigate('/citizens?filter=flagged')}
              />
            </Box>
          </Fade>
        </Grid>
      </Grid>

      {/* ID Status Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '500ms' }}>
            <Box>
              <DataCard
                title="Approved ID Cards"
                value={stats.approvedCards}
                subtitle="Ready for printing"
                icon={<ApprovedIcon />}
                color="success"
                onClick={() => navigate('/idcards?status=approved')}
              />
            </Box>
          </Fade>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '600ms' }}>
            <Box>
              <DataCard
                title="Expiring Soon"
                value={stats.expiringIds}
                subtitle="Next 30 days"
                icon={<PendingIcon />}
                color="warning"
                onClick={() => navigate('/idcards?filter=expiring')}
              />
            </Box>
          </Fade>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '700ms' }}>
            <Box>
              <DataCard
                title="Expired IDs"
                value={stats.expiredIds}
                subtitle="Over 30 days"
                icon={<CardIcon />}
                color="error"
                onClick={() => navigate('/idcards?filter=expired')}
              />
            </Box>
          </Fade>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Fade in={true} timeout={500} style={{ transitionDelay: '800ms' }}>
            <Box>
              <DataCard
                title="Migration Activity"
                value={migrationData.into + migrationData.outOf}
                subtitle={`${migrationData.into} in, ${migrationData.outOf} out`}
                icon={<TrendingUpIcon />}
                color="info"
                onClick={() => navigate('/reports/migration')}
              />
            </Box>
          </Fade>
        </Grid>
      </Grid>

      {/* Charts and Reports Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Population by Ketena Chart */}
        <Grid item xs={12} md={6}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '900ms' }}>
            <Box>
              <StyledCard
                title="Population by Ketena"
                icon={<AssessmentIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                {populationByKetena.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={populationByKetena}>
                      <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.text.secondary, 0.2)} />
                      <XAxis
                        dataKey="name"
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: alpha(theme.palette.text.secondary, 0.3) }}
                      />
                      <YAxis
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: alpha(theme.palette.text.secondary, 0.3) }}
                      />
                      <ChartTooltip
                        contentStyle={{
                          backgroundColor: theme.palette.background.paper,
                          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                          borderRadius: 8,
                          boxShadow: theme.shadows[3],
                        }}
                      />
                      <Bar dataKey="population" fill={theme.palette.primary.main} />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                    <Typography variant="body2" color="text.secondary">
                      No ketena data available
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>

        {/* Age Group Distribution Chart */}
        <Grid item xs={12} md={6}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '1000ms' }}>
            <Box>
              <StyledCard
                title="Age Group Distribution"
                icon={<GroupsIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                {ageGroupDistribution.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={ageGroupDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="count"
                      >
                        {ageGroupDistribution.map((entry, index) => {
                          const colors = [
                            theme.palette.primary.main,
                            theme.palette.secondary.main,
                            theme.palette.success.main,
                            theme.palette.warning.main,
                            theme.palette.error.main,
                            theme.palette.info.main,
                          ];
                          return (
                            <Cell
                              key={`cell-${index}`}
                              fill={colors[index % colors.length]}
                            />
                          );
                        })}
                      </Pie>
                      <ChartTooltip
                        contentStyle={{
                          backgroundColor: theme.palette.background.paper,
                          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                          borderRadius: 8,
                          boxShadow: theme.shadows[3],
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                    <Typography variant="body2" color="text.secondary">
                      No age group data available
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>
      </Grid>

      {/* Monthly Registration Trends */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '1100ms' }}>
            <Box>
              <StyledCard
                title="Monthly Registration Trends"
                icon={<TrendingUpIcon />}
                elevation={true}
              >
                {monthlyStats.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart
                      data={monthlyStats}
                      margin={{
                        top: 10,
                        right: 30,
                        left: 0,
                        bottom: 0,
                      }}
                    >
                      <defs>
                        <linearGradient id="colorIdCards" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8}/>
                          <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.text.secondary, 0.2)} />
                      <XAxis
                        dataKey="name"
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: alpha(theme.palette.text.secondary, 0.3) }}
                      />
                      <YAxis
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: alpha(theme.palette.text.secondary, 0.3) }}
                      />
                      <ChartTooltip
                        contentStyle={{
                          backgroundColor: theme.palette.background.paper,
                          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                          borderRadius: 8,
                          boxShadow: theme.shadows[3],
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="idCards"
                        name="ID Cards"
                        stroke={theme.palette.primary.main}
                        fillOpacity={1}
                        fill="url(#colorIdCards)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                    <Typography variant="body2" color="text.secondary">
                      No monthly data available
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>
      </Grid>

      {/* Summary Reports Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Top 3 Populated Ketenas */}
        <Grid item xs={12} md={4}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '1200ms' }}>
            <Box>
              <StyledCard
                title="Top 3 Populated Ketenas"
                icon={<TrendingUpIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                {topKetenas.length > 0 ? (
                  <Box sx={{ p: 2 }}>
                    {topKetenas.map((ketena, index) => (
                      <Box
                        key={ketena.name}
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          py: 1,
                          borderBottom: index < topKetenas.length - 1 ? '1px solid' : 'none',
                          borderColor: 'divider',
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              color: index === 0 ? 'gold' : index === 1 ? 'silver' : '#CD7F32',
                              fontWeight: 'bold',
                            }}
                          >
                            #{index + 1}
                          </Typography>
                          <Typography variant="subtitle2">{ketena.name}</Typography>
                        </Box>
                        <Typography variant="h6" color="primary.main" fontWeight="bold">
                          {ketena.population}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
                    <Typography variant="body2" color="text.secondary">
                      No ketena data available
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>

        {/* Gender Ratio */}
        <Grid item xs={12} md={4}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '1300ms' }}>
            <Box>
              <StyledCard
                title="Gender Ratio"
                icon={<GroupsIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-around', mb: 2 }}>
                    <Box>
                      <Typography variant="h4" color="primary.main" fontWeight="bold">
                        {stats.maleCount}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Male
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="h4" color="secondary.main" fontWeight="bold">
                        {stats.femaleCount}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Female
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="h6" color="text.primary">
                    Ratio: {stats.totalCitizens > 0 ?
                      `${((stats.maleCount / stats.totalCitizens) * 100).toFixed(1)}% : ${((stats.femaleCount / stats.totalCitizens) * 100).toFixed(1)}%`
                      : 'N/A'
                    }
                  </Typography>
                </Box>
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>

        {/* ID Status Summary */}
        <Grid item xs={12} md={4}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '1400ms' }}>
            <Box>
              <StyledCard
                title="ID Status Summary"
                icon={<CardIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                {statusDistribution.length > 0 ? (
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={statusDistribution}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {statusDistribution.map((entry, index) => {
                          const colors = {
                            'DRAFT': theme.palette.grey[500],
                            'PENDING APPROVAL': theme.palette.warning.main,
                            'APPROVED': theme.palette.success.main,
                            'REJECTED': theme.palette.error.main,
                            'PRINTING': theme.palette.info.main,
                            'PRINTED': theme.palette.primary.main,
                          };
                          return (
                            <Cell
                              key={`cell-${index}`}
                              fill={colors[entry.name] || theme.palette.grey[400]}
                            />
                          );
                        })}
                      </Pie>
                      <ChartTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
                    <Typography variant="body2" color="text.secondary">
                      No status data available
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>
      </Grid>

      {/* Recent Activity Section */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        {/* Recent Citizens */}
        <Grid item xs={12} md={6}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '1500ms' }}>
            <Box>
              <StyledCard
                title="Recent Citizens"
                icon={<GroupsIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                {recentCitizens.length > 0 ? (
                  <List sx={{ px: 1 }}>
                    {recentCitizens.map((citizen, index) => (
                      <ListItem
                        key={citizen.id}
                        alignItems="flex-start"
                        sx={{
                          borderRadius: 2,
                          mb: 1,
                          transition: 'all 0.2s ease',
                          cursor: 'pointer',
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.05),
                          }
                        }}
                        onClick={() => navigate(`/citizens/${citizen.id}`)}
                      >
                        <ListItemAvatar>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                            }}
                          >
                            <PersonIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography variant="subtitle2" fontWeight={600}>
                              {citizen.first_name} {citizen.middle_name} {citizen.last_name}
                            </Typography>
                          }
                          secondary={
                            <Typography variant="body2" color="text.secondary">
                              Registered: {new Date(citizen.created_at).toLocaleDateString()}
                            </Typography>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
                    <Typography variant="body2" color="text.secondary">
                      No recent citizens
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>

        {/* Recent ID Cards */}
        <Grid item xs={12} md={6}>
          <Zoom in={true} timeout={500} style={{ transitionDelay: '800ms' }}>
            <Box>
              <StyledCard
                title="Recent ID Card Activity"
                icon={<CardIcon />}
                elevation={true}
                sx={{ height: '100%' }}
              >
                {recentIdCards.length > 0 ? (
                  <List sx={{ px: 1 }}>
                    {recentIdCards.map((card, index) => {
                      const getStatusColor = (status) => {
                        switch (status) {
                          case 'approved': return theme.palette.success.main;
                          case 'pending_approval': return theme.palette.warning.main;
                          case 'rejected': return theme.palette.error.main;
                          case 'draft': return theme.palette.grey[500];
                          default: return theme.palette.info.main;
                        }
                      };

                      const getStatusIcon = (status) => {
                        switch (status) {
                          case 'approved': return <ApprovedIcon />;
                          case 'pending_approval': return <PendingIcon />;
                          default: return <CardIcon />;
                        }
                      };

                      const getStatusLabel = (status) => {
                        return status.replace('_', ' ').toUpperCase();
                      };

                      return (
                        <ListItem
                          key={card.id}
                          alignItems="flex-start"
                          sx={{
                            borderRadius: 2,
                            mb: 1,
                            transition: 'all 0.2s ease',
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.05),
                            }
                          }}
                          onClick={() => navigate(`/idcards/${card.id}`)}
                        >
                          <ListItemAvatar>
                            <Avatar
                              sx={{
                                bgcolor: alpha(getStatusColor(card.status), 0.1),
                                color: getStatusColor(card.status),
                              }}
                            >
                              {getStatusIcon(card.status)}
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Typography variant="subtitle2" fontWeight={600}>
                                {card.citizen_name || `Card #${card.card_number}`}
                              </Typography>
                            }
                            secondary={
                              <>
                                <Typography
                                  component="span"
                                  variant="body2"
                                  sx={{
                                    color: getStatusColor(card.status),
                                    fontWeight: 500,
                                  }}
                                >
                                  {getStatusLabel(card.status)}
                                </Typography>
                                <Typography
                                  component="span"
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {` — ${new Date(card.created_at).toLocaleDateString()}`}
                                </Typography>
                              </>
                            }
                          />
                        </ListItem>
                      );
                    })}
                  </List>
                ) : (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
                    <Typography variant="body2" color="text.secondary">
                      No recent ID cards
                    </Typography>
                  </Box>
                )}
              </StyledCard>
            </Box>
          </Zoom>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;

import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  Send as SendIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Description as DescriptionIcon,
  CloudUpload as UploadIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';

const ClearanceRequestForm = ({ open, onClose, citizen, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    destination_location: '',
    clearance_reason: 'relocation_outside_city',
    reason_description: ''
  });

  // Document upload state
  const [documents, setDocuments] = useState({
    application_letter: null,
    current_kebele_id: null,
    supporting_documents: null
  });

  const [documentPreviews, setDocumentPreviews] = useState({
    application_letter: null,
    current_kebele_id: null,
    supporting_documents: null
  });

  const clearanceReasons = [
    { value: 'relocation_outside_city', label: 'Relocation Outside City' },
    { value: 'marriage_outside_city', label: 'Marriage Outside City' },
    { value: 'work_outside_city', label: 'Work/Employment Outside City' },
    { value: 'education_outside_city', label: 'Education Outside City' },
    { value: 'family_outside_city', label: 'Family Reasons Outside City' },
    { value: 'emigration', label: 'Emigration/Moving Abroad' },
    { value: 'other', label: 'Other' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e, documentType) => {
    const file = e.target.files[0];
    if (file) {
      setDocuments(prev => ({
        ...prev,
        [documentType]: file
      }));

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setDocumentPreviews(prev => ({
        ...prev,
        [documentType]: previewUrl
      }));
    }
  };

  const resetForm = () => {
    setFormData({
      destination_location: '',
      clearance_reason: 'relocation_outside_city',
      reason_description: ''
    });
    setDocuments({
      application_letter: null,
      current_kebele_id: null,
      supporting_documents: null
    });
    setDocumentPreviews({
      application_letter: null,
      current_kebele_id: null,
      supporting_documents: null
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.destination_location.trim()) {
      setError('Please provide the destination location');
      return;
    }

    if (!formData.reason_description.trim()) {
      setError('Please provide a detailed reason for the clearance request');
      return;
    }

    if (!documents.application_letter) {
      setError('Please upload the application letter');
      return;
    }

    if (!documents.current_kebele_id) {
      setError('Please upload the current kebele ID card');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Step 1: Create the clearance request first
      const clearanceData = {
        citizen_id: citizen.id,
        citizen_name: citizen.full_name || `${citizen.first_name} ${citizen.last_name}`,
        citizen_digital_id: citizen.digital_id,
        destination_location: formData.destination_location.trim(),
        clearance_reason: formData.clearance_reason,
        reason_description: formData.reason_description.trim()
      };

      console.log('🔍 Creating clearance request:', clearanceData);

      const response = await axios.post('/api/tenants/clearances/', clearanceData);
      console.log('✅ Clearance request created:', response.data);

      const clearanceId = response.data.id;

      // Step 2: Upload documents
      const formDataUpload = new FormData();
      formDataUpload.append('application_letter', documents.application_letter);
      formDataUpload.append('current_kebele_id', documents.current_kebele_id);
      if (documents.supporting_documents) {
        formDataUpload.append('supporting_documents', documents.supporting_documents);
      }

      console.log('🔍 Uploading documents for clearance:', clearanceId);

      await axios.post(`/api/tenants/clearances/${clearanceId}/upload-documents/`, formDataUpload, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Documents uploaded successfully');

      // Step 3: Auto-approve and generate letter (simplified workflow)
      await axios.post(`/api/tenants/clearances/${clearanceId}/auto-approve/`);
      console.log('✅ Clearance auto-approved and letter generated');

      // Success
      resetForm();
      onClose();
      if (onSuccess) {
        onSuccess(response.data);
      }

    } catch (error) {
      console.error('Error creating clearance request:', error);
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else if (error.response?.data) {
        // Handle validation errors
        const errors = Object.values(error.response.data).flat();
        setError(errors.join(', '));
      } else {
        setError('Failed to create clearance request. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!citizen) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        pb: 2,
        backgroundColor: 'secondary.main',
        color: 'white'
      }}>
        <SendIcon />
        Create Clearance Request
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent sx={{ pt: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Citizen Information */}
          <Box sx={{
            p: 2,
            backgroundColor: 'grey.50',
            borderRadius: 1,
            mb: 3,
            border: '1px solid',
            borderColor: 'grey.200'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <PersonIcon color="primary" />
              <Typography variant="h6" fontWeight="bold">
                Citizen Information
              </Typography>
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Full Name
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {citizen.full_name || `${citizen.first_name} ${citizen.last_name}`}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Digital ID
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {citizen.digital_id || 'Not assigned'}
                </Typography>
              </Grid>
            </Grid>
          </Box>

          {/* Clearance Details */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <LocationIcon color="primary" />
              <Typography variant="h6" fontWeight="bold">
                Clearance Details
              </Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Destination Location"
                  name="destination_location"
                  value={formData.destination_location}
                  onChange={handleInputChange}
                  required
                  placeholder="e.g., Addis Ababa, Dire Dawa, USA, etc."
                  helperText="Where is the citizen moving to?"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel>Clearance Reason</InputLabel>
                  <Select
                    name="clearance_reason"
                    value={formData.clearance_reason}
                    onChange={handleInputChange}
                    label="Clearance Reason"
                  >
                    {clearanceReasons.map((reason) => (
                      <MenuItem key={reason.value} value={reason.value}>
                        {reason.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Detailed Reason"
                  name="reason_description"
                  value={formData.reason_description}
                  onChange={handleInputChange}
                  required
                  multiline
                  rows={3}
                  placeholder="Please provide detailed information about why the citizen needs clearance..."
                />
              </Grid>
            </Grid>
          </Box>

          {/* Document Upload */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <UploadIcon color="primary" />
              <Typography variant="h6" fontWeight="bold">
                Required Documents
              </Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, border: '1px dashed', borderColor: 'grey.300' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Application Letter *
                  </Typography>
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileChange(e, 'application_letter')}
                    style={{ width: '100%', marginBottom: '8px' }}
                  />
                  {documents.application_letter && (
                    <Typography variant="caption" color="success.main">
                      ✓ {documents.application_letter.name}
                    </Typography>
                  )}
                </Paper>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Paper sx={{ p: 2, border: '1px dashed', borderColor: 'grey.300' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Current Kebele ID Card *
                  </Typography>
                  <input
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileChange(e, 'current_kebele_id')}
                    style={{ width: '100%', marginBottom: '8px' }}
                  />
                  {documents.current_kebele_id && (
                    <Typography variant="caption" color="success.main">
                      ✓ {documents.current_kebele_id.name}
                    </Typography>
                  )}
                </Paper>
              </Grid>

              <Grid item xs={12}>
                <Paper sx={{ p: 2, border: '1px dashed', borderColor: 'grey.300' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Supporting Documents (Optional)
                  </Typography>
                  <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                    Job offer letter, marriage certificate, admission letter, etc.
                  </Typography>
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileChange(e, 'supporting_documents')}
                    style={{ width: '100%', marginBottom: '8px' }}
                  />
                  {documents.supporting_documents && (
                    <Typography variant="caption" color="success.main">
                      ✓ {documents.supporting_documents.name}
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </Box>

          {/* Note about simplified workflow */}
          <Box sx={{ mb: 3 }}>
            <Alert severity="info">
              <Typography variant="body2">
                The clearance request will be automatically approved and the letter generated instantly.
                No manual approval process is required.
              </Typography>
            </Alert>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            sx={{
              bgcolor: 'secondary.main',
              '&:hover': { bgcolor: 'secondary.dark' }
            }}
          >
            {loading ? 'Creating...' : 'Create Clearance Request'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ClearanceRequestForm;

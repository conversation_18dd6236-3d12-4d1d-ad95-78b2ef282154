import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Paper,
} from '@mui/material';
import {
  PhotoCamera as PhotoCameraIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

const PhotoCaptureStep = ({ formik, loading }) => {
  const [webcamOpen, setWebcamOpen] = useState(false);
  const [webcamError, setWebcamError] = useState('');
  const [previewUrl, setPreviewUrl] = useState(formik.values.photo || '');
  const [stream, setStream] = useState(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);

  // Handle file upload
  const handleFileUpload = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file.');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.');
        return;
      }

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);

      // Convert to base64 for storage
      const reader = new FileReader();
      reader.onload = (e) => {
        formik.setFieldValue('photo', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  }, [formik]);

  // Start webcam
  const startWebcam = useCallback(async () => {
    try {
      setWebcamError('');
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: 1280,
          height: 720,
          facingMode: "user"
        },
        audio: false
      });

      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing webcam:', error);
      setWebcamError('Unable to access camera. Please check your camera permissions or try uploading a photo instead.');
    }
  }, []);

  // Stop webcam
  const stopWebcam = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  }, [stream]);

  // Capture photo from webcam
  const capturePhoto = useCallback(() => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert canvas to base64 image
      const imageSrc = canvas.toDataURL('image/jpeg', 0.8);

      setPreviewUrl(imageSrc);
      formik.setFieldValue('photo', imageSrc);
      closeWebcam();
    }
  }, [formik]);

  // Remove photo
  const removePhoto = useCallback(() => {
    setPreviewUrl('');
    formik.setFieldValue('photo', '');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [formik]);

  // Open webcam
  const openWebcam = useCallback(() => {
    setWebcamError('');
    setWebcamOpen(true);
    startWebcam();
  }, [startWebcam]);

  // Close webcam
  const closeWebcam = useCallback(() => {
    setWebcamOpen(false);
    setWebcamError('');
    stopWebcam();
  }, [stopWebcam]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopWebcam();
    };
  }, [stopWebcam]);

  return (
    <Card>
      {/* Header with blue background */}
      <Box sx={{
        bgcolor: '#2196f3',
        color: 'white',
        p: 2,
        display: 'flex',
        alignItems: 'center'
      }}>
        <PhotoCameraIcon sx={{ mr: 1 }} />
        <Typography variant="h6" component="h2">
          Photo Capture
        </Typography>
      </Box>

      <CardContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Capture or upload a photo of the citizen. This photo will be used for ID card generation.
        </Typography>

        <Grid container spacing={3}>
          {/* Photo Preview */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={2}
              sx={{
                p: 2,
                textAlign: 'center',
                minHeight: 300,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                bgcolor: '#f5f5f5'
              }}
            >
              {previewUrl ? (
                <Box sx={{ position: 'relative', width: '100%', maxWidth: 250 }}>
                  <img
                    src={previewUrl}
                    alt="Citizen Photo"
                    style={{
                      width: '100%',
                      height: 'auto',
                      maxHeight: 250,
                      objectFit: 'cover',
                      borderRadius: 8,
                      border: '2px solid #2196f3'
                    }}
                  />
                  <IconButton
                    onClick={removePhoto}
                    sx={{
                      position: 'absolute',
                      top: -10,
                      right: -10,
                      bgcolor: 'error.main',
                      color: 'white',
                      '&:hover': { bgcolor: 'error.dark' }
                    }}
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center' }}>
                  <PhotoCameraIcon sx={{ fontSize: 64, color: '#ccc', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary">
                    No photo captured yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Use the buttons below to capture or upload a photo
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Photo Capture Options */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* Webcam Capture Button */}
              <Button
                variant="contained"
                startIcon={<PhotoCameraIcon />}
                onClick={openWebcam}
                disabled={loading}
                size="large"
                sx={{ py: 2 }}
              >
                Capture from Camera
              </Button>

              {/* File Upload Button */}
              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                onClick={() => fileInputRef.current?.click()}
                disabled={loading}
                size="large"
                sx={{ py: 2 }}
              >
                Upload from Device
              </Button>

              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />

              {/* Remove Photo Button */}
              {previewUrl && (
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={removePhoto}
                  disabled={loading}
                  size="large"
                  sx={{ py: 2 }}
                >
                  Remove Photo
                </Button>
              )}

              {/* Photo Requirements */}
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Photo Requirements:</strong>
                  <br />• Clear, front-facing photo
                  <br />• Good lighting
                  <br />• Maximum file size: 5MB
                  <br />• Supported formats: JPG, PNG, GIF
                </Typography>
              </Alert>

              {/* Form Error */}
              {formik.touched.photo && formik.errors.photo && (
                <Alert severity="error">
                  {formik.errors.photo}
                </Alert>
              )}
            </Box>
          </Grid>
        </Grid>

        {/* Webcam Dialog */}
        <Dialog
          open={webcamOpen}
          onClose={closeWebcam}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            Capture Photo
            <IconButton onClick={closeWebcam}>
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            {webcamError ? (
              <Alert severity="error" sx={{ mb: 2 }}>
                {webcamError}
              </Alert>
            ) : (
              <Box sx={{ textAlign: 'center' }}>
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  style={{
                    width: '100%',
                    maxWidth: 500,
                    height: 'auto',
                    borderRadius: 8,
                    border: '2px solid #2196f3'
                  }}
                />
                {/* Hidden canvas for photo capture */}
                <canvas
                  ref={canvasRef}
                  style={{ display: 'none' }}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
            {!webcamError && (
              <>
                <Button
                  variant="contained"
                  startIcon={<PhotoCameraIcon />}
                  onClick={capturePhoto}
                  size="large"
                >
                  Capture Photo
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    closeWebcam();
                    setTimeout(() => openWebcam(), 100);
                  }}
                  size="large"
                >
                  Retry Camera
                </Button>
              </>
            )}
            <Button onClick={closeWebcam} size="large">
              Cancel
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default PhotoCaptureStep;

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Menu,
} from '@mui/material';
import {
  Business as BusinessIcon,
  PersonAdd as PersonAddIcon,
  People as PeopleIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Lock as LockIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';

const KebeleUserManagement = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const navigate = useNavigate();

  // State management
  const [kebeles, setKebeles] = useState([]);
  const [selectedKebele, setSelectedKebele] = useState(null);
  const [kebeleUsers, setKebeleUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [editing, setEditing] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [passwordUser, setPasswordUser] = useState(null);
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [selectedUserForMenu, setSelectedUserForMenu] = useState(null);

  // Form state
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'clerk',
    password: '',
    phone_number: ''
  });

  const [editUser, setEditUser] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    role: 'clerk',
    phone_number: ''
  });

  const [passwordForm, setPasswordForm] = useState({
    new_password: '',
    confirm_password: ''
  });

  useEffect(() => {
    fetchKebeles();
  }, []);

  useEffect(() => {
    if (selectedKebele) {
      fetchKebeleUsers(selectedKebele.id);
    }
  }, [selectedKebele]);

  const fetchKebeles = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch kebeles under the current subcity
      const response = await axios.get('/api/tenants/', {
        params: {
          type: 'kebele',
          parent: user.tenant?.id
        }
      });

      setKebeles(response.data.results || []);
    } catch (error) {
      console.error('Failed to fetch kebeles:', error);
      setError('Failed to load kebeles. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchKebeleUsers = async (kebeleId) => {
    try {
      const response = await axios.get(`/api/tenants/${kebeleId}/users/`);
      setKebeleUsers(response.data || []);
    } catch (error) {
      console.error('Failed to fetch kebele users:', error);
      setKebeleUsers([]);
    }
  };

  const handleCreateUser = async () => {
    if (!selectedKebele) return;

    try {
      setCreating(true);

      // Create username with domain if not already present
      let username = newUser.username;

      // Get the kebele's domain from domains array or construct it
      let kebeleDomain = selectedKebele.primary_domain;
      if (!kebeleDomain && selectedKebele.schema_name) {
        kebeleDomain = `${selectedKebele.schema_name}.goid.local`;
      }

      if (kebeleDomain && !username.includes('@')) {
        username = `${username}@${kebeleDomain}`;
      }

      const userData = {
        ...newUser,
        username,
        password2: newUser.password, // Add password confirmation
        tenant: selectedKebele.id
      };

      console.log('🔍 Creating user with data:', userData);
      console.log('🔍 Target kebele:', selectedKebele);

      await axios.post(`/api/tenants/${selectedKebele.id}/create_user/`, userData);

      // Refresh users list
      fetchKebeleUsers(selectedKebele.id);

      // Reset form and close dialog
      setNewUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: 'clerk',
        password: '',
        phone_number: ''
      });
      setCreateUserDialogOpen(false);

    } catch (error) {
      console.error('Failed to create user:', error);
      console.error('Error response:', error.response?.data);

      // Handle validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format validation errors
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(`Validation errors:\n${errorMessages}`);
        } else {
          setError(errorData.detail || 'Failed to create user. Please try again.');
        }
      } else {
        setError('Failed to create user. Please try again.');
      }
    } finally {
      setCreating(false);
    }
  };

  const handleInputChange = (field, value) => {
    setNewUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setEditUser({
      username: user.username,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      phone_number: user.phone_number || ''
    });
    setEditUserDialogOpen(true);
  };

  const handleUpdateUser = async () => {
    if (!selectedKebele || !editingUser) return;

    try {
      setEditing(true);

      const updateData = {
        user_id: editingUser.id,
        ...editUser
      };

      console.log('🔍 Updating user with data:', updateData);
      console.log('🔍 Target kebele:', selectedKebele);

      await axios.patch(`/api/tenants/${selectedKebele.id}/update_user/`, updateData);

      // Refresh users list
      fetchKebeleUsers(selectedKebele.id);

      // Close dialog and reset state
      setEditUserDialogOpen(false);
      setEditingUser(null);
      setEditUser({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: 'clerk',
        phone_number: ''
      });

    } catch (error) {
      console.error('Failed to update user:', error);
      console.error('Error response:', error.response?.data);

      // Handle validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object' && !errorData.detail) {
          // Format validation errors
          const errorMessages = Object.entries(errorData)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          setError(`Validation errors:\n${errorMessages}`);
        } else {
          setError(errorData.detail || 'Failed to update user. Please try again.');
        }
      } else {
        setError('Failed to update user. Please try again.');
      }
    } finally {
      setEditing(false);
    }
  };

  const handlePasswordInputChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleChangePassword = (user) => {
    setPasswordUser(user);
    setPasswordForm({
      new_password: '',
      confirm_password: ''
    });
    setPasswordDialogOpen(true);
    setUserMenuAnchor(null);
  };

  const handleUpdatePassword = async () => {
    if (!selectedKebele || !passwordUser) return;

    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setError('Passwords do not match');
      return;
    }

    if (passwordForm.new_password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    try {
      setChangingPassword(true);

      const passwordData = {
        user_id: passwordUser.id,
        new_password: passwordForm.new_password,
        confirm_password: passwordForm.confirm_password
      };

      console.log('🔍 Changing password for user:', passwordUser.email);
      console.log('🔍 Target kebele:', selectedKebele);

      await axios.patch(`/api/tenants/${selectedKebele.id}/change_user_password/`, passwordData);

      // Close dialog and reset state
      setPasswordDialogOpen(false);
      setPasswordUser(null);
      setPasswordForm({
        new_password: '',
        confirm_password: ''
      });

      // Show success message
      setError(''); // Clear any previous errors
      // You could add a success snackbar here

    } catch (error) {
      console.error('Failed to change password:', error);
      console.error('Error response:', error.response?.data);

      // Handle validation errors
      if (error.response?.data) {
        const errorData = error.response.data;
        setError(errorData.detail || 'Failed to change password. Please try again.');
      } else {
        setError('Failed to change password. Please try again.');
      }
    } finally {
      setChangingPassword(false);
    }
  };

  const handleUserMenuOpen = (event, user) => {
    setUserMenuAnchor(event.currentTarget);
    setSelectedUserForMenu(user);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
    setSelectedUserForMenu(null);
  };

  const getKebeleTypeChip = (type) => {
    return (
      <Chip
        icon={<HomeIcon />}
        label="Kebele"
        color="success"
        size="small"
        variant="outlined"
      />
    );
  };

  const getRoleChip = (role) => {
    const colors = {
      clerk: 'primary',
      kebele_leader: 'secondary',
      kebele_admin: 'warning'
    };

    return (
      <Chip
        label={role.replace('_', ' ').toUpperCase()}
        color={colors[role] || 'default'}
        size="small"
      />
    );
  };

  if (!hasPermission('manage_users')) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          You don't have permission to manage users.
        </Alert>
      </Box>
    );
  }

  if (user?.role !== 'subcity_admin' && user?.role !== 'superadmin' && !user?.is_superuser) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          This page is only available for subcity administrators and superadmins.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
          Kebele User Management
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Manage users for kebeles under {user.tenant?.name}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Kebeles List */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <BusinessIcon sx={{ mr: 1 }} />
              Kebeles ({kebeles.length})
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : kebeles.length === 0 ? (
              <Typography variant="body2" color="textSecondary" sx={{ py: 4, textAlign: 'center' }}>
                No kebeles found under this subcity.
              </Typography>
            ) : (
              <Grid container spacing={2}>
                {kebeles.map((kebele) => (
                  <Grid item xs={12} key={kebele.id}>
                    <Card
                      variant={selectedKebele?.id === kebele.id ? "outlined" : "elevation"}
                      sx={{
                        cursor: 'pointer',
                        border: selectedKebele?.id === kebele.id ? 2 : 1,
                        borderColor: selectedKebele?.id === kebele.id ? 'primary.main' : 'divider'
                      }}
                      onClick={() => setSelectedKebele(kebele)}
                    >
                      <CardContent sx={{ pb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {kebele.name}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              Schema: {kebele.schema_name}
                            </Typography>
                          </Box>
                          {getKebeleTypeChip(kebele.type)}
                        </Box>
                        <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                          Users: {kebele.users_count || 0}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Grid>

        {/* Selected Kebele Users */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                <PeopleIcon sx={{ mr: 1 }} />
                {selectedKebele ? `${selectedKebele.name} Users` : 'Select a Kebele'}
              </Typography>
              {selectedKebele && (
                <Button
                  variant="contained"
                  startIcon={<PersonAddIcon />}
                  onClick={() => setCreateUserDialogOpen(true)}
                  size="small"
                >
                  Add User
                </Button>
              )}
            </Box>
            <Divider sx={{ mb: 2 }} />

            {!selectedKebele ? (
              <Typography variant="body2" color="textSecondary" sx={{ py: 4, textAlign: 'center' }}>
                Select a kebele to view and manage its users.
              </Typography>
            ) : kebeleUsers.length === 0 ? (
              <Typography variant="body2" color="textSecondary" sx={{ py: 4, textAlign: 'center' }}>
                No users found for this kebele.
              </Typography>
            ) : (
              <List>
                {kebeleUsers.map((user) => (
                  <ListItem key={user.id} divider>
                    <ListItemAvatar>
                      <Avatar>
                        {user.first_name?.charAt(0) || user.username?.charAt(0)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${user.first_name} ${user.last_name}`.trim() || user.username}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {user.email}
                          </Typography>
                          {getRoleChip(user.role)}
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Tooltip title="User Actions">
                        <IconButton size="small" onClick={(e) => handleUserMenuOpen(e, user)}>
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* User Actions Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
      >
        <MenuItem onClick={() => {
          handleEditUser(selectedUserForMenu);
          handleUserMenuClose();
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit User
        </MenuItem>
        <MenuItem onClick={() => {
          handleChangePassword(selectedUserForMenu);
          handleUserMenuClose();
        }}>
          <LockIcon sx={{ mr: 1 }} />
          Change Password
        </MenuItem>
      </Menu>

      {/* Create User Dialog */}
      <Dialog open={createUserDialogOpen} onClose={() => setCreateUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Create User for {selectedKebele?.name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={newUser.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                helperText={`Will be: ${newUser.username}@${selectedKebele?.primary_domain || selectedKebele?.schema_name + '.goid.local' || 'domain.com'}`}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={newUser.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={newUser.first_name}
                onChange={(e) => handleInputChange('first_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={newUser.last_name}
                onChange={(e) => handleInputChange('last_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={newUser.role}
                  label="Role"
                  onChange={(e) => handleInputChange('role', e.target.value)}
                >
                  <MenuItem value="clerk">Clerk</MenuItem>
                  <MenuItem value="kebele_leader">Kebele Leader</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={newUser.phone_number}
                onChange={(e) => handleInputChange('phone_number', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={newUser.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateUser}
            variant="contained"
            disabled={creating || !newUser.username || !newUser.password}
            startIcon={creating ? <CircularProgress size={20} /> : <PersonAddIcon />}
          >
            {creating ? 'Creating...' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editUserDialogOpen} onClose={() => setEditUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Edit User: {editingUser?.first_name} {editingUser?.last_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={editUser.username}
                onChange={(e) => handleEditInputChange('username', e.target.value)}
                helperText="Username cannot be changed after creation"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={editUser.email}
                onChange={(e) => handleEditInputChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={editUser.first_name}
                onChange={(e) => handleEditInputChange('first_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={editUser.last_name}
                onChange={(e) => handleEditInputChange('last_name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={editUser.role}
                  label="Role"
                  onChange={(e) => handleEditInputChange('role', e.target.value)}
                >
                  <MenuItem value="clerk">Clerk</MenuItem>
                  <MenuItem value="kebele_leader">Kebele Leader</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={editUser.phone_number}
                onChange={(e) => handleEditInputChange('phone_number', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditUserDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateUser}
            variant="contained"
            disabled={editing || !editUser.email || !editUser.first_name || !editUser.last_name}
            startIcon={editing ? <CircularProgress size={20} /> : <EditIcon />}
          >
            {editing ? 'Updating...' : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Change Password Dialog */}
      <Dialog open={passwordDialogOpen} onClose={() => setPasswordDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Change Password for {passwordUser?.first_name} {passwordUser?.last_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="New Password"
                type="password"
                value={passwordForm.new_password}
                onChange={(e) => handlePasswordInputChange('new_password', e.target.value)}
                helperText="Password must be at least 8 characters long"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Confirm New Password"
                type="password"
                value={passwordForm.confirm_password}
                onChange={(e) => handlePasswordInputChange('confirm_password', e.target.value)}
                error={passwordForm.confirm_password && passwordForm.new_password !== passwordForm.confirm_password}
                helperText={
                  passwordForm.confirm_password && passwordForm.new_password !== passwordForm.confirm_password
                    ? "Passwords do not match"
                    : "Re-enter the new password"
                }
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdatePassword}
            variant="contained"
            disabled={
              changingPassword ||
              !passwordForm.new_password ||
              !passwordForm.confirm_password ||
              passwordForm.new_password !== passwordForm.confirm_password ||
              passwordForm.new_password.length < 8
            }
            startIcon={changingPassword ? <CircularProgress size={20} /> : <LockIcon />}
          >
            {changingPassword ? 'Changing...' : 'Change Password'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KebeleUserManagement;

# Generated manually for adding digital_id unique constraint

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0007_create_remaining_tables'),
    ]

    operations = [
        # Add unique constraint to digital_id after data is populated
        migrations.AlterField(
            model_name='citizen',
            name='digital_id',
            field=models.CharField(max_length=50, unique=True, blank=True, null=True),
        ),
    ]

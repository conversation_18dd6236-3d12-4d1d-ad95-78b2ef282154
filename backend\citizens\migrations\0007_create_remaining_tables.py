# Generated manually for creating remaining tables

from django.db import migrations, models
import django.db.models.deletion


def citizen_photo_path(instance, filename):
    """Generate file path for citizen photos"""
    return f"citizens/{instance.citizen.get_directory_path()}/photos/{filename}"


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0006_create_related_tables'),
    ]

    operations = [
        # Create Spouse model
        migrations.CreateModel(
            name='Spouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.Char<PERSON>ield(max_length=100)),
                ('first_name_am', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.IntegerField(blank=True, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident', models.BooleanField(default=False, help_text='Indicates whether the spouse is a resident of the city.')),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spouse_records', to='citizens.citizen')),
                ('linked_citizen', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_spouse', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Spouse',
                'verbose_name_plural': 'Spouses',
            },
        ),
        
        # Create Biometric model
        migrations.CreateModel(
            name='Biometric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('left_hand_fingerprint', models.TextField(blank=True, null=True)),
                ('right_hand_fingerprint', models.TextField(blank=True, null=True)),
                ('left_thumb_fingerprint', models.TextField(blank=True, null=True)),
                ('right_thumb_fingerprint', models.TextField(blank=True, null=True)),
                ('left_eye_iris_scan', models.TextField(blank=True, null=True)),
                ('right_eye_iris_scan', models.TextField(blank=True, null=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='biometric_record', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Biometric',
                'verbose_name_plural': 'Biometrics',
            },
        ),
        
        # Create Photo model
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to=citizen_photo_path)),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photo_record', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'Photo',
                'verbose_name_plural': 'Photos',
            },
        ),
        
        # Add unique constraints
        migrations.AddConstraint(
            model_name='emergencycontact',
            constraint=models.UniqueConstraint(fields=('citizen', 'phone'), name='unique_emergency_contact_phone'),
        ),
        migrations.AddConstraint(
            model_name='spouse',
            constraint=models.UniqueConstraint(fields=('citizen', 'phone'), name='unique_spouse_phone'),
        ),
    ]

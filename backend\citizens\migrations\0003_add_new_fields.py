# Generated manually for step-by-step migration

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0002_initial'),
    ]

    operations = [
        # Add new fields without constraints first
        migrations.AddField(
            model_name='citizen',
            name='digital_id',
            field=models.Char<PERSON>ield(max_length=50, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='first_name_am',
            field=models.CharField(max_length=100, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='middle_name_am',
            field=models.CharField(max_length=100, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='last_name_am',
            field=models.CharField(max_length=100, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='phone',
            field=models.Char<PERSON>ield(max_length=20, blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='citizen',
            name='religion',
            field=models.Integer<PERSON>ield(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='status',
            field=models.IntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='subcity',
            field=models.IntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='kebele',
            field=models.IntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='ketena',
            field=models.IntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='region',
            field=models.IntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='employment',
            field=models.CharField(max_length=100, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='employee_type',
            field=models.CharField(max_length=100, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='organization_name',
            field=models.CharField(max_length=200, blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='is_resident',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        # Add temporary fields for data conversion
        migrations.AddField(
            model_name='citizen',
            name='marital_status_new',
            field=models.IntegerField(null=True, blank=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='nationality_new',
            field=models.IntegerField(null=True, blank=True),
        ),
    ]

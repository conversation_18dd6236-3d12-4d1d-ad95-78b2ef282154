import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Divider,
  Paper,
  useTheme,
  useMediaQuery,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Fade,
  Slide,
  Zoom,
  Stack,
} from '@mui/material';
import {
  Menu as MenuIcon,
  KeyboardArrowRight as ArrowIcon,
  CreditCard as CardIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  People as PeopleIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Computer as ComputerIcon,
  Login as LoginIcon,
} from '@mui/icons-material';
import { useTheme as useAppTheme } from '../../contexts/ThemeContext';

const LandingPage = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { mode, toggleTheme } = useAppTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const navItems = [
    { label: 'Home', href: '#' },
    { label: 'About', href: '#about' },
    { label: 'Contact', href: '#contact' },
    { label: 'Login', href: '/login', isButton: true },
  ];

  const features = [
    {
      title: 'Secure Digital IDs',
      description: 'Create tamper-proof digital ID cards with advanced security features including QR verification.',
      icon: <CardIcon sx={{ fontSize: 60, color: 'primary.main' }} />,
    },
    {
      title: 'Multi-Tenant System',
      description: 'Hierarchical structure supporting city, subcity, and kebele level administration and data management.',
      icon: <PeopleIcon sx={{ fontSize: 60, color: 'primary.main' }} />,
    },
    {
      title: 'Advanced Security',
      description: 'Role-based access control with comprehensive audit trails and data protection measures.',
      icon: <SecurityIcon sx={{ fontSize: 60, color: 'primary.main' }} />,
    },
    {
      title: 'Efficient Workflows',
      description: 'Streamlined processes for citizen registration, ID approval, and issuance with real-time updates.',
      icon: <SpeedIcon sx={{ fontSize: 60, color: 'primary.main' }} />,
    },
  ];

  const steps = [
    {
      title: 'Citizen Registration',
      description: 'Citizens are registered at local kebele offices with verification of personal information and documents.',
      image: '/images/registration.svg',
    },
    {
      title: 'ID Card Creation',
      description: 'Digital ID cards are created with secure QR codes and submitted for approval.',
      image: '/images/id-creation.svg',
    },
    {
      title: 'Verification & Approval',
      description: 'ID cards are verified and approved by authorized personnel through a secure workflow.',
      image: '/images/verification.svg',
    },
    {
      title: 'Issuance & Usage',
      description: 'Approved ID cards are issued to citizens and can be verified digitally anywhere.',
      image: '/images/issuance.svg',
    },
  ];

  const testimonials = [
    {
      quote: "GoID has revolutionized our citizen identification process, reducing fraud and improving service delivery.",
      author: "Addis Ababa City Administration",
      role: "City Government",
    },
    {
      quote: "The hierarchical structure allows us to efficiently manage IDs while maintaining local control and oversight.",
      author: "Bole Subcity Office",
      role: "Subcity Administration",
    },
    {
      quote: "Our citizens now receive their IDs faster and with greater security than ever before.",
      author: "Kebele 01/02 Office",
      role: "Local Administration",
    },
  ];

  const drawer = (
    <Box onClick={handleDrawerToggle} sx={{ textAlign: 'center' }}>
      <Typography variant="h6" sx={{ my: 2, fontWeight: 'bold', color: 'primary.main' }}>
        NeoCamelot
      </Typography>
      <Divider />
      <List>
        {navItems.map((item) => (
          <ListItem key={item.label} disablePadding>
            <ListItemButton
              sx={{ textAlign: 'center' }}
              onClick={() => {
                if (item.href === '#') {
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                } else if (item.href.startsWith('#')) {
                  document.querySelector(item.href)?.scrollIntoView({ behavior: 'smooth' });
                } else {
                  navigate(item.href);
                }
              }}
            >
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ minHeight: '100vh' }}>
      {/* Navigation */}
      <AppBar
        position="fixed"
        color="primary"
        elevation={scrolled ? 4 : 0}
        sx={{
          backgroundColor: '#1976d2',
          transition: 'all 0.3s ease',
          boxShadow: scrolled ? theme.shadows[4] : 'none',
        }}
      >
        <Container>
          <Toolbar disableGutters>
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontWeight: 'bold',
                color: 'white',
              }}
            >
              NeoCamelot
            </Typography>

            {isMobile ? (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="end"
                onClick={handleDrawerToggle}
                sx={{
                  color: 'white',
                }}
              >
                <MenuIcon />
              </IconButton>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {navItems.map((item, index) => (
                  item.isButton ? (
                    <Button
                      key={item.label}
                      variant="contained"
                      color={item.isPrimary ? "warning" : "inherit"}
                      onClick={() => navigate(item.href)}
                      sx={{
                        ml: 1,
                        backgroundColor: item.isPrimary ? '#ff9800' : 'rgba(255, 255, 255, 0.1)',
                        color: 'white',
                        '&:hover': {
                          backgroundColor: item.isPrimary ? '#f57c00' : 'rgba(255, 255, 255, 0.2)',
                        }
                      }}
                      startIcon={item.isPrimary ? null : <LoginIcon />}
                    >
                      {item.label}
                    </Button>
                  ) : (
                    <Button
                      key={item.label}
                      color="inherit"
                      onClick={() => {
                        if (item.href === '#') {
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                        } else if (item.href.startsWith('#')) {
                          document.querySelector(item.href)?.scrollIntoView({ behavior: 'smooth' });
                        } else {
                          navigate(item.href);
                        }
                      }}
                      sx={{
                        ml: 2,
                        color: 'white',
                        '&:hover': {
                          color: 'rgba(255, 255, 255, 0.8)',
                        },
                      }}
                    >
                      {item.label}
                    </Button>
                  )
                ))}
              </Box>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      <Drawer
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better mobile performance
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 240 },
        }}
      >
        {drawer}
      </Drawer>

      {/* Hero Section */}
      <Box
        sx={{
          position: 'relative',
          minHeight: '600px',
          display: 'flex',
          alignItems: 'center',
          backgroundImage: 'linear-gradient(135deg, #1976d2 0%, #0d47a1 100%)',
          color: 'white',
          overflow: 'hidden',
          pt: 8,
          pb: 6,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'url(/images/pattern.svg)',
            backgroundSize: 'cover',
            opacity: 0.1,
          },
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ position: 'relative', zIndex: 2, py: 4 }}>
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={7}>
                <Fade in={true} timeout={1000}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: { xs: 'center', md: 'flex-start' } }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        component="img"
                        src="/images/id-card-icon.svg"
                        alt="ID Card Icon"
                        sx={{
                          width: 80,
                          height: 80,
                          mr: 2,
                        }}
                      />
                      <Box>
                        <Typography
                          variant="h3"
                          component="h1"
                          gutterBottom
                          sx={{
                            fontWeight: 'bold',
                            textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                            mb: 0,
                          }}
                        >
                          Gondar ID Card Management System
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            textShadow: '1px 1px 2px rgba(0,0,0,0.3)',
                            borderLeft: '3px solid white',
                            pl: 2,
                          }}
                        >
                          A comprehensive solution for issuing, tracking, and managing citizen ID cards across all administrative levels
                        </Typography>
                      </Box>
                    </Box>

                    <Box
                      sx={{
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        p: 3,
                        borderRadius: 2,
                        backdropFilter: 'blur(10px)',
                        mt: 3,
                        width: '100%',
                        textAlign: 'center',
                      }}
                    >
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        Our platform provides end-to-end solutions for all of Gondar city's identification needs, from kebele centers to subcity and city administration.
                      </Typography>
                      <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        spacing={2}
                        justifyContent="center"
                        sx={{ mt: 3 }}
                      >
                        <Button
                          variant="outlined"
                          size="large"
                          onClick={() => navigate('/login')}
                          sx={{
                            py: 1.5,
                            px: 3,
                            borderRadius: 2,
                            textTransform: 'none',
                            fontSize: '1rem',
                            borderColor: 'white',
                            color: 'white',
                            '&:hover': {
                              borderColor: 'white',
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                            }
                          }}
                        >
                          Login
                        </Button>
                        <Button
                          variant="outlined"
                          size="large"
                          endIcon={<ArrowIcon />}
                          onClick={() => document.querySelector('#features')?.scrollIntoView({ behavior: 'smooth' })}
                          sx={{
                            py: 1.5,
                            px: 3,
                            borderRadius: 2,
                            textTransform: 'none',
                            fontSize: '1rem',
                            borderColor: 'white',
                            color: 'white',
                            '&:hover': {
                              borderColor: 'white',
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                            }
                          }}
                        >
                          Learn More
                        </Button>
                      </Stack>
                    </Box>
                  </Box>
                </Fade>
              </Grid>
              <Grid item xs={12} md={5}>
                <Zoom in={true} timeout={1500} style={{ transitionDelay: '500ms' }}>
                  <Box
                    component="img"
                    src="/images/id-cards-hero.svg"
                    alt="ID Cards Management System"
                    sx={{
                      width: '100%',
                      maxWidth: 400,
                      height: 'auto',
                      display: 'block',
                      margin: '0 auto',
                      filter: 'drop-shadow(8px 8px 20px rgba(0,0,0,0.4))',
                      transition: 'transform 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'scale(1.05) rotate(1deg)',
                      },
                    }}
                  />
                </Zoom>
              </Grid>
            </Grid>
          </Box>
        </Container>
        <Box
          sx={{
            position: 'absolute',
            bottom: -1,
            left: 0,
            width: '100%',
            height: '50px',
            backgroundColor: 'background.paper',
            borderTopLeftRadius: '50%',
            borderTopRightRadius: '50%',
            transform: 'scale(1.5, 1)',
          }}
        />
      </Box>

      {/* Features Section */}
      <Box id="features" sx={{ py: 10, backgroundColor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            align="center"
            gutterBottom
            sx={{ fontWeight: 'bold', mb: 6 }}
          >
            Key Features
          </Typography>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Zoom in={true} timeout={1000} style={{ transitionDelay: `${index * 200}ms` }}>
                  <Card
                    elevation={4}
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center',
                      p: 3,
                      borderRadius: 4,
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: theme.shadows[10],
                      },
                    }}
                  >
                    <Box sx={{ mb: 2 }}>
                      {feature.icon}
                    </Box>
                    <Typography variant="h5" component="h3" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </Card>
                </Zoom>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* How It Works Section */}
      <Box id="how-it-works" sx={{ py: 10, backgroundColor: theme.palette.mode === 'light' ? '#f5f5f5' : '#1e1e1e' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            align="center"
            gutterBottom
            sx={{ fontWeight: 'bold', mb: 6 }}
          >
            How It Works
          </Typography>

          {steps.map((step, index) => (
            <Fade in={true} timeout={1000} key={index} style={{ transitionDelay: `${index * 200}ms` }}>
              <Box>
                <Grid
                  container
                  spacing={4}
                  alignItems="center"
                  sx={{
                    mb: 8,
                    flexDirection: index % 2 === 0 ? 'row' : 'row-reverse',
                  }}
                >
                  <Grid item xs={12} md={6}>
                    <Box
                      component="img"
                      src={step.image}
                      alt={step.title}
                      sx={{
                        width: '100%',
                        maxWidth: 500,
                        height: 'auto',
                        display: 'block',
                        margin: '0 auto',
                        filter: 'drop-shadow(0px 5px 10px rgba(0,0,0,0.1))',
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ p: 2 }}>
                      <Typography
                        variant="h4"
                        component="h3"
                        gutterBottom
                        sx={{
                          fontWeight: 'bold',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <Box
                          component="span"
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            backgroundColor: 'primary.main',
                            color: 'white',
                            mr: 2,
                            fontWeight: 'bold',
                          }}
                        >
                          {index + 1}
                        </Box>
                        {step.title}
                      </Typography>
                      <Typography variant="body1" paragraph sx={{ fontSize: '1.1rem' }}>
                        {step.description}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          ))}
        </Container>
      </Box>

      {/* Use Cases / Testimonials Section */}
      <Box id="use-cases" sx={{ py: 10, backgroundColor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            align="center"
            gutterBottom
            sx={{ fontWeight: 'bold', mb: 6 }}
          >
            Success Stories
          </Typography>

          <Grid container spacing={4} justifyContent="center">
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Zoom in={true} timeout={1000} style={{ transitionDelay: `${index * 200}ms` }}>
                  <Card
                    elevation={4}
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      p: 4,
                      borderRadius: 4,
                      position: 'relative',
                      overflow: 'visible',
                      '&::before': {
                        content: '"""',
                        position: 'absolute',
                        top: -30,
                        left: 20,
                        fontSize: '6rem',
                        color: 'primary.main',
                        opacity: 0.2,
                        fontFamily: 'Georgia, serif',
                      },
                    }}
                  >
                    <Typography variant="body1" paragraph sx={{ fontSize: '1.1rem', fontStyle: 'italic', mb: 3, zIndex: 1 }}>
                      "{testimonial.quote}"
                    </Typography>
                    <Box sx={{ mt: 'auto' }}>
                      <Typography variant="h6" component="p" gutterBottom>
                        {testimonial.author}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {testimonial.role}
                      </Typography>
                    </Box>
                  </Card>
                </Zoom>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Call to Action Section */}
      <Box
        sx={{
          py: 10,
          backgroundImage: 'linear-gradient(135deg, #1976d2 0%, #0d47a1 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'url(/images/pattern.svg)',
            backgroundSize: 'cover',
            opacity: 0.1,
          },
        }}
      >
        <Container maxWidth="md">
          <Box sx={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>
            <Typography
              variant="h3"
              component="h2"
              gutterBottom
              sx={{ fontWeight: 'bold' }}
            >
              Ready to Transform Your ID System?
            </Typography>
            <Typography variant="h6" paragraph sx={{ mb: 4, maxWidth: 700, mx: 'auto' }}>
              Join the digital revolution in citizen identification and management with GoID's secure, efficient platform.
            </Typography>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              onClick={() => navigate('/login')}
              sx={{
                py: 1.5,
                px: 4,
                borderRadius: 2,
                textTransform: 'none',
                fontSize: '1.1rem',
                boxShadow: theme.shadows[8],
              }}
            >
              Get Started Now
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Footer */}
      <Box sx={{ py: 5, backgroundColor: theme.palette.mode === 'light' ? '#0a1929' : '#000' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 2 }}>
                GoID
              </Typography>
              <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)', mb: 2 }}>
                Secure, efficient, and scalable digital ID management system for modern Ethiopia.
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 2 }}>
                Quick Links
              </Typography>
              <Box component="ul" sx={{ p: 0, m: 0, listStyle: 'none' }}>
                {navItems.map((item) => (
                  <Box component="li" key={item.label} sx={{ mb: 1 }}>
                    <Button
                      color="inherit"
                      onClick={() => item.href.startsWith('#') ?
                        document.querySelector(item.href).scrollIntoView({ behavior: 'smooth' }) :
                        navigate(item.href)
                      }
                      sx={{
                        color: 'rgba(255,255,255,0.7)',
                        p: 0,
                        '&:hover': {
                          color: 'white',
                          backgroundColor: 'transparent',
                        },
                      }}
                    >
                      {item.label}
                    </Button>
                  </Box>
                ))}
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 2 }}>
                Contact
              </Typography>
              <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)', mb: 1 }}>
                Email: <EMAIL>
              </Typography>
              <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)', mb: 1 }}>
                Phone: +251 11 123 4567
              </Typography>
              <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)' }}>
                Address: Addis Ababa, Ethiopia
              </Typography>
            </Grid>
          </Grid>
          <Divider sx={{ my: 3, backgroundColor: 'rgba(255,255,255,0.1)' }} />
          <Typography variant="body2" align="center" sx={{ color: 'rgba(255,255,255,0.5)' }}>
            © {new Date().getFullYear()} GoID. All rights reserved.
          </Typography>
        </Container>
      </Box>
    </Box>
  );
};

export default LandingPage;

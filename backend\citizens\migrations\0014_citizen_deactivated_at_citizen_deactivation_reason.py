# Generated by Django 4.2.7 on 2025-06-05 18:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0013_citizen_original_digital_id_citizen_transfer_date_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='deactivated_at',
            field=models.DateTimeField(blank=True, help_text='Date when citizen was deactivated', null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='deactivation_reason',
            field=models.CharField(blank=True, choices=[('transfer_completed', 'Transfer Completed'), ('clearance_issued', 'Clearance Letter Issued'), ('deceased', 'Deceased'), ('other', 'Other')], help_text='Reason for deactivation', max_length=50, null=True),
        ),
    ]

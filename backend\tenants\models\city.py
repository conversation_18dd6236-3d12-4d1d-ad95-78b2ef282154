from django.db import models
from django.conf import settings as django_settings
from ..utils import Timestamp

class Country(Timestamp):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=3, unique=True)  # 3-character unique code for the country
    capital_city = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = "Country"
        verbose_name_plural = "Countries"
        ordering = ['name', 'code']

class Region(Timestamp):
    country = models.ForeignKey(Country, on_delete=models.CASCADE, related_name='regions')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=5, unique=True)  # 5-character unique code for the region

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name = "Region"
        verbose_name_plural = "Regions"
        ordering = ['name', 'code']

class CityAdministration(Timestamp):
    city_code = models.CharField(max_length=50, unique=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True, blank=True)
    city_name = models.CharField(max_length=100)
    logo = models.ImageField(upload_to='logos/', blank=True, null=True)
    motto_slogan = models.TextField(blank=True, null=True)
    city_intro = models.TextField(blank=True, null=True)
    mayor_name = models.CharField(max_length=100, blank=True, null=True)
    deputy_mayor = models.CharField(max_length=100, blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)  # renamed to contact_email
    contact_phone = models.CharField(max_length=20, blank=True, null=True)  # renamed to contact_phone
    google_maps_url = models.URLField(blank=True, null=True)
    area_sq_km = models.FloatField(blank=True, null=True)
    elevation_meters = models.IntegerField(blank=True, null=True)
    timezone = models.CharField(max_length=50, default="EAT")
    website = models.URLField(blank=True, null=True)
    headquarter_address = models.TextField(blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    established_date = models.DateField(blank=True, null=True)
    is_resident = models.BooleanField(default=False)  # Boolean field to check if the city is a resident
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    tenant = models.OneToOneField('tenants.Tenant', on_delete=models.CASCADE, related_name='city_profile')

    def __str__(self):
        return f"{self.city_name} ({self.city_code})"

    class Meta:
        verbose_name = "City Administration"
        verbose_name_plural = "City Administrations"
        ordering = ['city_name', 'city_code']

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Visibility as ViewIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Print as PrintIcon,
  Assignment as IssueIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';

const ClearancesList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [clearances, setClearances] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchClearances();
    fetchStats();
  }, []);

  const fetchClearances = async () => {
    try {
      const response = await axios.get('/api/tenants/clearances/');
      setClearances(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching clearances:', error);
      setError('Failed to load clearances');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/tenants/clearances/stats/');
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching clearance stats:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'info';
      case 'rejected':
        return 'error';
      case 'issued':
        return 'success';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'approved':
        return '✅';
      case 'rejected':
        return '❌';
      case 'issued':
        return '📄';
      case 'cancelled':
        return '🚫';
      default:
        return '❓';
    }
  };

  const handleView = (clearanceId) => {
    navigate(`/clearances/${clearanceId}`);
  };

  const handleReview = async (clearanceId, action) => {
    const notes = prompt(`Please provide review notes for ${action}:`);
    if (notes === null) return; // User cancelled

    try {
      await axios.post(`/api/tenants/clearances/${clearanceId}/review/`, {
        action: action,
        review_notes: notes
      });
      
      fetchClearances();
      fetchStats();
      alert(`Clearance ${action}d successfully!`);
    } catch (error) {
      console.error(`Error ${action}ing clearance:`, error);
      alert(`Failed to ${action} clearance. Please try again.`);
    }
  };

  const handleIssue = async (clearanceId) => {
    if (!window.confirm('Are you sure you want to issue the clearance letter?')) {
      return;
    }

    try {
      await axios.post(`/api/tenants/clearances/${clearanceId}/issue/`);
      
      fetchClearances();
      fetchStats();
      alert('Clearance letter issued successfully!');
    } catch (error) {
      console.error('Error issuing clearance:', error);
      alert('Failed to issue clearance letter. Please try again.');
    }
  };

  const canReview = (clearance) => {
    return user?.role === 'subcity_admin' && clearance.status === 'pending';
  };

  const canIssue = (clearance) => {
    return (user?.role === 'kebele_leader' || user?.role === 'clerk') && 
           clearance.status === 'approved';
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Clearance Requests
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total
              </Typography>
              <Typography variant="h4">
                {stats.total || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Pending
              </Typography>
              <Typography variant="h4" color="warning.main">
                {stats.pending || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Approved
              </Typography>
              <Typography variant="h4" color="info.main">
                {stats.approved || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Issued
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.issued || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Rejected
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.rejected || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Clearances Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Clearance ID</TableCell>
                <TableCell>Citizen</TableCell>
                <TableCell>Destination</TableCell>
                <TableCell>Reason</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {clearances.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Typography variant="body2" color="text.secondary">
                      No clearance requests found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                clearances.map((clearance) => (
                  <TableRow key={clearance.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {clearance.clearance_id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {clearance.citizen_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ID: {clearance.citizen_digital_id || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {clearance.destination_location}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {clearance.clearance_reason_display}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${getStatusIcon(clearance.status)} ${clearance.status_display}`}
                        color={getStatusColor(clearance.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(clearance.created_at).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleView(clearance.id)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>

                        {canReview(clearance) && (
                          <>
                            <Tooltip title="Approve">
                              <IconButton
                                size="small"
                                color="success"
                                onClick={() => handleReview(clearance.id, 'approve')}
                              >
                                <ApproveIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Reject">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleReview(clearance.id, 'reject')}
                              >
                                <RejectIcon />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}

                        {canIssue(clearance) && (
                          <Tooltip title="Issue Clearance Letter">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleIssue(clearance.id)}
                            >
                              <IssueIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Container>
  );
};

export default ClearancesList;

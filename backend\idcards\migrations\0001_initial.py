# Generated by Django 4.2.7 on 2025-05-22 17:02

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='IDCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number', models.CharField(editable=False, max_length=20, unique=True)),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], default='draft', max_length=20)),
                ('qr_code', models.ImageField(blank=True, null=True, upload_to='id_card_qr_codes/')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='id_card_pdfs/')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  Button,
  Typography,
  Paper,
  Container,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Business as BusinessIcon,
  LocationCity as CityIcon,
  Apartment as SubcityIcon,
  Home as KebeleIcon,
  ArrowBack as BackIcon,
  ArrowForward as NextIcon,
  Check as CheckIcon,
} from '@mui/icons-material';
import TenantTypeSelection from './registration/TenantTypeSelection';
import CityRegistrationForm from './registration/CityRegistrationForm';
import SubcityRegistrationForm from './registration/SubcityRegistrationForm';
import KebeleRegistrationForm from './registration/KebeleRegistrationForm';
import AdminAccountForm from './registration/AdminAccountForm';
import ReviewSubmit from './registration/ReviewSubmit';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const getSteps = (tenantType) => {
  const baseSteps = [
    'Select Tenant Type',
    'Tenant Information',
  ];

  // Skip admin account step for kebele tenants
  if (tenantType === 'kebele') {
    return [...baseSteps, 'Review & Submit'];
  } else {
    return [...baseSteps, 'Administrator Account', 'Review & Submit'];
  }
};

const TenantRegistration = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [tenantType, setTenantType] = useState('');
  const [tenantData, setTenantData] = useState({});
  const [adminData, setAdminData] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [parentOptions, setParentOptions] = useState([]);

  // Check if user has permission to register tenants
  const canRegisterTenants = isAuthenticated && (user?.is_superuser || user?.role === 'superadmin');

  // Redirect if user doesn't have permission
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!canRegisterTenants) {
      setError('You do not have permission to register tenants. Only superadmins can register tenants.');
      return;
    }
  }, [isAuthenticated, canRegisterTenants, navigate]);

  useEffect(() => {
    // If tenant type is subcity, fetch available cities
    if (tenantType === 'subcity') {
      const fetchCities = async () => {
        try {
          const response = await axios.get('/api/tenants/registration/available_cities/');
          setParentOptions(response.data);
        } catch (err) {
          console.error('Failed to fetch cities:', err);
          setError('Failed to fetch available cities. Please try again.');
        }
      };
      fetchCities();
    }

    // If tenant type is kebele, fetch available subcities
    if (tenantType === 'kebele') {
      const fetchSubcities = async () => {
        try {
          const response = await axios.get('/api/tenants/registration/available_subcities/');
          setParentOptions(response.data);
        } catch (err) {
          console.error('Failed to fetch subcities:', err);
          setError('Failed to fetch available subcities. Please try again.');
        }
      };
      fetchSubcities();
    }
  }, [tenantType]);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleTenantTypeSelect = (type) => {
    setTenantType(type);
    setTenantData({ ...tenantData, type });
  };

  const handleTenantDataChange = (data) => {
    setTenantData({ ...tenantData, ...data });
  };

  const handleAdminDataChange = (data) => {
    setAdminData({ ...adminData, ...data });
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');

    try {
      // Step 1: Create the tenant
      let tenantResponse;

      if (tenantType === 'city') {
        // Create FormData for file upload
        const formData = new FormData();
        formData.append('tenant_name', tenantData.tenant_name || tenantData.city_name);
        formData.append('city_name', tenantData.city_name);
        formData.append('domain_name', tenantData.domain_name || '');

        // Debug logging
        console.log('🔍 City Registration Debug:');
        console.log('tenantData:', tenantData);
        console.log('domain_name being sent:', tenantData.domain_name);

        // Convert country and region to integers
        if (tenantData.country) {
          formData.append('country', parseInt(tenantData.country));
        }
        if (tenantData.region) {
          formData.append('region', parseInt(tenantData.region));
        }

        formData.append('contact_email', tenantData.contact_email);
        formData.append('contact_phone', tenantData.contact_phone || '');
        formData.append('mayor_name', tenantData.mayor_name || '');
        formData.append('deputy_mayor', tenantData.deputy_mayor || '');
        formData.append('motto_slogan', tenantData.motto_slogan || '');
        formData.append('city_intro', tenantData.city_intro || '');
        formData.append('website', tenantData.website || '');
        formData.append('headquarter_address', tenantData.headquarter_address || '');
        formData.append('postal_code', tenantData.postal_code || '');
        formData.append('google_maps_url', tenantData.google_maps_url || '');
        formData.append('area_sq_km', tenantData.area_sq_km || '');
        formData.append('elevation_meters', tenantData.elevation_meters || '');
        formData.append('timezone', tenantData.timezone || 'EAT');

        // Format date as YYYY-MM-DD
        if (tenantData.established_date) {
          const date = new Date(tenantData.established_date);
          const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD format
          formData.append('established_date', formattedDate);
        }

        formData.append('is_resident', tenantData.is_resident ? 'true' : 'false');
        formData.append('is_active', tenantData.is_active !== false ? 'true' : 'false');

        // Add logo if available
        if (tenantData.logo) {
          formData.append('logo', tenantData.logo);
        }

        tenantResponse = await axios.post('/api/tenants/cities/', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      } else if (tenantType === 'subcity') {
        // Debug logging
        console.log('🔍 Subcity Registration Debug:');
        console.log('tenantData:', tenantData);
        console.log('domain_name being sent:', tenantData.domain_name);

        tenantResponse = await axios.post('/api/tenants/subcities/', {
          tenant_name: tenantData.tenant_name || tenantData.name,
          domain_name: tenantData.domain_name,
          name: tenantData.name,
          city_id: tenantData.city_id,
          code: tenantData.code,
          is_active: tenantData.is_active !== false,
        });
      } else if (tenantType === 'kebele') {
        tenantResponse = await axios.post('/api/tenants/kebeles/', {
          tenant_name: tenantData.tenant_name || tenantData.name,
          domain_name: tenantData.domain_name,
          name: tenantData.name,
          subcity_id: tenantData.subcity_id,
          code: tenantData.code,
          is_active: tenantData.is_active !== false,
        });
      }

      // Step 2: Create the admin user for the tenant (only for city and subcity)
      if (tenantResponse && tenantResponse.data && tenantType !== 'kebele') {
        const tenantId = tenantResponse.data.tenant_id || tenantResponse.data.tenant;

        // Debug logging for response
        console.log('🔍 Tenant Response Debug:');
        console.log('tenantResponse.data:', tenantResponse.data);
        console.log('tenant_id:', tenantId);

        // Get domain name from response or tenant data
        const domainName = tenantResponse.data.domain_name;
        console.log('domainName from response:', domainName);

        // Create username with domain suffix if not already present
        let username = adminData.username;
        console.log('original username:', username);
        if (domainName && !username.includes('@')) {
          username = `${username}@${domainName}`;
          console.log('✅ Username with domain:', username);
        } else {
          console.log('❌ Domain not appended. domainName:', domainName, 'username includes @:', username.includes('@'));
        }

        await axios.post(`/api/tenants/${tenantId}/create_admin/`, {
          email: adminData.email,
          username: username,
          password: adminData.password,
          password2: adminData.confirmPassword || adminData.password,
          first_name: adminData.firstName,
          last_name: adminData.lastName,
          role: tenantType === 'city' ? 'city_admin' : 'subcity_admin',
          phone_number: adminData.phone,
        });
      }

      // For kebele tenants, skip admin creation - parent subcity admin will create users
      setSuccess(true);
      setTimeout(() => {
        navigate('/tenants');
      }, 3000);
    } catch (err) {
      console.error('Registration failed:', err);
      console.error('Error response:', err.response);
      console.error('Error data:', err.response?.data);

      let errorMessage = 'Registration failed. Please try again.';

      if (err.response?.status === 403) {
        errorMessage = 'You do not have permission to register tenants. Please contact an administrator.';
      } else if (err.response?.status === 405) {
        errorMessage = 'Method not allowed. Please check your permissions or contact an administrator.';
      } else if (err.response?.data) {
        if (typeof err.response.data === 'string') {
          errorMessage = err.response.data;
        } else if (err.response.data.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.response.data.error) {
          errorMessage = err.response.data.error;
        } else {
          errorMessage = JSON.stringify(err.response.data);
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getStepContent = (step) => {
    const steps = getSteps(tenantType);
    const stepName = steps[step];

    switch (stepName) {
      case 'Select Tenant Type':
        return (
          <TenantTypeSelection
            selectedType={tenantType}
            onSelect={handleTenantTypeSelect}
          />
        );
      case 'Tenant Information':
        if (tenantType === 'city') {
          return (
            <CityRegistrationForm
              data={tenantData}
              onChange={handleTenantDataChange}
            />
          );
        } else if (tenantType === 'subcity') {
          return (
            <SubcityRegistrationForm
              data={tenantData}
              onChange={handleTenantDataChange}
              parentOptions={parentOptions}
            />
          );
        } else if (tenantType === 'kebele') {
          return (
            <KebeleRegistrationForm
              data={tenantData}
              onChange={handleTenantDataChange}
              parentOptions={parentOptions}
            />
          );
        }
        return <Typography>Please select a tenant type first</Typography>;
      case 'Administrator Account':
        return (
          <AdminAccountForm
            data={adminData}
            onChange={handleAdminDataChange}
            tenantData={tenantData}
            tenantType={tenantType}
          />
        );
      case 'Review & Submit':
        return (
          <ReviewSubmit
            tenantType={tenantType}
            tenantData={tenantData}
            adminData={adminData}
            parentOptions={parentOptions}
          />
        );
      default:
        return 'Unknown step';
    }
  };

  const isNextDisabled = () => {
    const steps = getSteps(tenantType);
    const stepName = steps[activeStep];

    switch (stepName) {
      case 'Select Tenant Type':
        return !tenantType;
      case 'Tenant Information':
        if (tenantType === 'city') {
          return !tenantData.city_name || !tenantData.contact_email || !tenantData.domain_name;
        } else if (tenantType === 'subcity') {
          return !tenantData.name || !tenantData.city_id || !tenantData.domain_name;
        } else if (tenantType === 'kebele') {
          return !tenantData.name || !tenantData.subcity_id || !tenantData.domain_name;
        }
        break;
      case 'Administrator Account':
        return !adminData.email || !adminData.username || !adminData.password ||
               !adminData.confirmPassword || adminData.password !== adminData.confirmPassword;
      case 'Review & Submit':
        return false;
      default:
        return false;
    }
    return false;
  };

  return (
    <Container maxWidth="lg">
      <Paper
        elevation={3}
        sx={{
          p: 4,
          mt: 4,
          mb: 4,
          borderRadius: 2,
          background: `linear-gradient(to right bottom, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.98)})`,
          backdropFilter: 'blur(10px)',
          boxShadow: theme.shadows[10],
        }}
      >
        <Typography variant="h4" component="h1" align="center" gutterBottom fontWeight="bold">
          Tenant Registration
        </Typography>
        <Typography variant="subtitle1" align="center" color="text.secondary" paragraph>
          Register a new {tenantType || 'tenant'} in the GoID Card Management System
        </Typography>

        {/* Debug information */}
        {process.env.NODE_ENV === 'development' && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Debug Info:</strong><br />
              User: {user?.email || 'Not logged in'}<br />
              Role: {user?.role || 'No role'}<br />
              Is Superuser: {user?.is_superuser ? 'Yes' : 'No'}<br />
              Can Register Tenants: {canRegisterTenants ? 'Yes' : 'No'}
            </Typography>
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Registration successful! Redirecting to tenants list...
          </Alert>
        )}

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {getSteps(tenantType).map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <Box sx={{ mt: 4, mb: 4 }}>
          {getStepContent(activeStep)}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={activeStep === 0 ? () => navigate('/tenants') : handleBack}
            disabled={loading}
          >
            {activeStep === 0 ? 'Cancel' : 'Back'}
          </Button>

          <Button
            variant="contained"
            endIcon={activeStep === getSteps(tenantType).length - 1 ? <CheckIcon /> : <NextIcon />}
            onClick={activeStep === getSteps(tenantType).length - 1 ? handleSubmit : handleNext}
            disabled={isNextDisabled() || loading}
          >
            {loading ? (
              <>
                <CircularProgress size={24} sx={{ mr: 1 }} />
                Processing...
              </>
            ) : activeStep === getSteps(tenantType).length - 1 ? (
              'Submit'
            ) : (
              'Next'
            )}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default TenantRegistration;

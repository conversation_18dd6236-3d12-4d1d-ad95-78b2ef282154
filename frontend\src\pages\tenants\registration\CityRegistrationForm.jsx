import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Divider,
  Switch,
  FormControlLabel,
  Button,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { CloudUpload as CloudUploadIcon } from '@mui/icons-material';
import axios from '../../../utils/axios';
import { useSharedData } from '../../../contexts/SharedDataContext';
import SharedDataDropdown from '../../../components/common/SharedDataDropdown';

const CityRegistrationForm = ({ data, onChange }) => {
  const [logoFile, setLogoFile] = useState(null);
  const { loading: sharedDataLoading } = useSharedData();

  const handleCountryChange = (e) => {
    const countryId = e.target.value;
    onChange({ ...data, country: countryId, region: '' });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange({ ...data, [name]: value });
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    onChange({ ...data, [name]: checked });
  };

  const handleDateChange = (date) => {
    onChange({ ...data, established_date: date });
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      onChange({ ...data, logo: file });
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold">
        City Administration Information
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Enter the details for the city administration.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Basic Information</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="city_name"
            name="city_name"
            label="City Name"
            value={data.city_name || ''}
            onChange={handleChange}
            placeholder="e.g., Addis Ababa"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="tenant_name"
            name="tenant_name"
            label="Tenant Name"
            value={data.tenant_name || data.city_name || ''}
            onChange={handleChange}
            placeholder="e.g., Addis Ababa City Administration"
            helperText="Name used for the tenant schema"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="domain_name"
            name="domain_name"
            label="Domain Name"
            value={data.domain_name || ''}
            onChange={handleChange}
            placeholder="e.g., addisababa.goid.gov.et"
            helperText="Domain name for tenant access (without http://)"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Box>
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="logo-upload"
              type="file"
              onChange={handleLogoChange}
            />
            <label htmlFor="logo-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                sx={{ mb: 1 }}
              >
                Upload Logo
              </Button>
            </label>
            {logoFile && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                Selected file: {logoFile.name}
              </Typography>
            )}
            <FormHelperText>Upload city logo (recommended size: 200x200px)</FormHelperText>
          </Box>
        </Grid>

        <Grid item xs={12} md={6}>
          <SharedDataDropdown
            dataType="countries"
            label="Country"
            name="country"
            id="country"
            value={data.country || ''}
            onChange={handleCountryChange}
            required
            disabled={sharedDataLoading}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <SharedDataDropdown
            dataType="regions"
            label="Region/State"
            name="region"
            id="region"
            value={data.region || ''}
            onChange={handleChange}
            disabled={!data.country || sharedDataLoading}
            filterBy="country"
            filterValue={data.country}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <DatePicker
            label="Established Date"
            value={data.established_date || null}
            onChange={handleDateChange}
            slotProps={{
              textField: {
                fullWidth: true,
                variant: 'outlined'
              }
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="area_sq_km"
            name="area_sq_km"
            label="Area (sq km)"
            type="number"
            value={data.area_sq_km || ''}
            onChange={handleChange}
            placeholder="e.g., 540"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="elevation_meters"
            name="elevation_meters"
            label="Elevation (meters)"
            type="number"
            value={data.elevation_meters || ''}
            onChange={handleChange}
            placeholder="e.g., 2355"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="timezone"
            name="timezone"
            label="Timezone"
            value={data.timezone || 'EAT'}
            onChange={handleChange}
            placeholder="e.g., EAT"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            id="motto_slogan"
            name="motto_slogan"
            label="Motto/Slogan"
            value={data.motto_slogan || ''}
            onChange={handleChange}
            placeholder="City motto or slogan"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            multiline
            rows={3}
            id="city_intro"
            name="city_intro"
            label="City Description"
            value={data.city_intro || ''}
            onChange={handleChange}
            placeholder="Brief description of the city"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Contact Information</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="contact_email"
            name="contact_email"
            label="Contact Email"
            type="email"
            value={data.contact_email || ''}
            onChange={handleChange}
            placeholder="<EMAIL>"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="contact_phone"
            name="contact_phone"
            label="Contact Phone"
            value={data.contact_phone || ''}
            onChange={handleChange}
            placeholder="+251 11 123 4567"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="website"
            name="website"
            label="Website"
            value={data.website || ''}
            onChange={handleChange}
            placeholder="https://www.city.gov.et"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="headquarter_address"
            name="headquarter_address"
            label="Headquarter Address"
            value={data.headquarter_address || ''}
            onChange={handleChange}
            placeholder="Main street, City center"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="postal_code"
            name="postal_code"
            label="Postal Code"
            value={data.postal_code || ''}
            onChange={handleChange}
            placeholder="e.g., 1000"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="google_maps_url"
            name="google_maps_url"
            label="Google Maps URL"
            value={data.google_maps_url || ''}
            onChange={handleChange}
            placeholder="https://maps.google.com/?q=..."
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Leadership</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="mayor_name"
            name="mayor_name"
            label="Mayor Name"
            value={data.mayor_name || ''}
            onChange={handleChange}
            placeholder="Full name of the mayor"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="deputy_mayor"
            name="deputy_mayor"
            label="Deputy Mayor Name"
            value={data.deputy_mayor || ''}
            onChange={handleChange}
            placeholder="Full name of the deputy mayor"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Status</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={data.is_resident || false}
                onChange={handleSwitchChange}
                name="is_resident"
                color="primary"
              />
            }
            label="Is Resident City"
          />
          <FormHelperText>Indicates whether the city is a resident city</FormHelperText>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={data.is_active !== false}
                onChange={handleSwitchChange}
                name="is_active"
                color="primary"
              />
            }
            label="Is Active"
          />
          <FormHelperText>Indicates whether the city is active</FormHelperText>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CityRegistrationForm;

# Generated by Django 4.2.7 on 2025-05-22 23:10

from django.db import migrations, models
import django.db.models.deletion
import tenants.models.citizen


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0003_child_citizen_citizenstatus_cityadministration_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CurrentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Current status of the citizen', max_length=50, unique=True)),
            ],
            options={
                'verbose_name': 'Current Status',
                'verbose_name_plural': 'Current Statuses',
            },
        ),
        migrations.CreateModel(
            name='Relationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Relationship',
                'verbose_name_plural': 'Relationships',
            },
        ),
        migrations.AlterModelOptions(
            name='child',
            options={'verbose_name': 'Child', 'verbose_name_plural': 'Children'},
        ),
        migrations.AddField(
            model_name='child',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the child is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='child',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_child', to='tenants.citizen'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='employee_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.employmenttype'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='employment',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='citizen',
            name='father',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_as_father', to='tenants.parent'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='first_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='house_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='id_expiry_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='id_issue_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the citizen is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='kebele',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.kebele'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='ketena',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='tenants.ketena'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='last_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='marital_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.maritalstatus'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='middle_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='middle_name_am',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='mother',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children_as_mother', to='tenants.parent'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='nationality',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.country'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='organization_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.region'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='religion',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.religion'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='status',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='tenants.citizenstatus'),
        ),
        migrations.AddField(
            model_name='citizen',
            name='subcity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.subcity'),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the emergency contact is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_emergency_contact', to='tenants.citizen'),
        ),
        migrations.AddField(
            model_name='spouse',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the spouse is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='spouse',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_spouse', to='tenants.citizen'),
        ),
        migrations.AlterField(
            model_name='document',
            name='document_file',
            field=models.FileField(upload_to=tenants.models.citizen.citizen_document_path),
        ),
        migrations.AlterField(
            model_name='spouse',
            name='citizen',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spouse_records', to='tenants.citizen'),
        ),
        migrations.CreateModel(
            name='Photo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('photo', models.ImageField(upload_to=tenants.models.citizen.citizen_photo_path)),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photo_record', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Photo',
                'verbose_name_plural': 'Photos',
            },
        ),
        migrations.CreateModel(
            name='Biometric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('left_hand_fingerprint', models.BinaryField(blank=True, null=True)),
                ('right_hand_fingerprint', models.BinaryField(blank=True, null=True)),
                ('left_thumb_fingerprint', models.BinaryField(blank=True, null=True)),
                ('right_thumb_fingerprint', models.BinaryField(blank=True, null=True)),
                ('left_eye_iris_scan', models.BinaryField(blank=True, null=True)),
                ('right_eye_iris_scan', models.BinaryField(blank=True, null=True)),
                ('citizen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='biometric_record', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Biometric',
                'verbose_name_plural': 'Biometrics',
            },
        ),
        migrations.AddField(
            model_name='citizen',
            name='current_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.currentstatus'),
        ),
    ]

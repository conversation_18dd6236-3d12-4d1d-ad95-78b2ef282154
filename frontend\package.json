{"name": "goid-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.18", "@mui/lab": "^5.0.0-alpha.153", "@mui/material": "^5.14.18", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "formik": "^2.4.5", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.19.0", "recharts": "^2.9.3", "yup": "^1.3.2"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0"}}
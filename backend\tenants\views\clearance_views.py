from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from django_tenants.utils import schema_context

from workflows.models import CitizenClearanceRequest
from tenants.serializers.clearance_serializers import (
    CitizenClearanceRequestSerializer,
    CitizenClearanceRequestCreateSerializer,
    CitizenClearanceReviewSerializer,
    CitizenClearanceDocumentUploadSerializer
)
from tenants.models import Tenant
from users.models import User


class CitizenClearanceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing citizen clearance requests.
    
    Workflow:
    1. POST /clearances/ - Kebele leader creates clearance request
    2. GET /clearances/ - List clearances (filtered by user's role and kebele)
    3. POST /clearances/{id}/review/ - Subcity admin approves/rejects
    4. POST /clearances/{id}/issue/ - Issue clearance letter
    5. POST /clearances/{id}/cancel/ - Cancel clearance request
    """
    
    serializer_class = CitizenClearanceRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter clearances based on user role and tenant."""
        user = self.request.user
        
        if user.is_superuser:
            # Superadmin can see all clearances
            return CitizenClearanceRequest.objects.all()
        
        if not hasattr(user, 'tenant') or not user.tenant:
            return CitizenClearanceRequest.objects.none()
        
        tenant = user.tenant
        
        if tenant.type == 'kebele':
            # Kebele users can see clearances from their kebele
            return CitizenClearanceRequest.objects.filter(source_kebele=tenant)
        
        elif tenant.type == 'subcity':
            # Subcity users can see clearances from their child kebeles
            child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')
            return CitizenClearanceRequest.objects.filter(source_kebele__in=child_kebeles)
        
        elif tenant.type == 'city':
            # City users can see all clearances in their city
            city_kebeles = Tenant.objects.filter(
                parent__parent=tenant,
                type='kebele'
            )
            return CitizenClearanceRequest.objects.filter(source_kebele__in=city_kebeles)
        
        return CitizenClearanceRequest.objects.none()
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return CitizenClearanceRequestCreateSerializer
        elif self.action == 'review':
            return CitizenClearanceReviewSerializer
        elif self.action == 'upload_documents':
            return CitizenClearanceDocumentUploadSerializer
        return CitizenClearanceRequestSerializer
    
    def perform_create(self, serializer):
        """Create clearance request with proper tenant context."""
        user = self.request.user
        
        # Validate user has permission to create clearance requests
        if not user.tenant or user.tenant.type != 'kebele':
            raise ValueError("Only kebele users can create clearance requests")
        
        if user.role not in ['kebele_leader', 'clerk']:
            raise ValueError("Only kebele leaders and clerks can create clearance requests")
        
        # Check for existing pending clearance for this citizen
        citizen_id = serializer.validated_data['citizen_id']
        existing_clearance = CitizenClearanceRequest.objects.filter(
            source_kebele=user.tenant,
            citizen_id=citizen_id,
            status__in=['pending', 'approved']
        ).first()
        
        if existing_clearance:
            raise ValueError("There is already a pending clearance request for this citizen")
        
        # Create clearance request
        serializer.save(
            source_kebele=user.tenant,
            requested_by=user
        )
    
    @action(detail=True, methods=['post'])
    def review(self, request, pk=None):
        """Review clearance request (approve/reject) - Subcity admin only."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if not user.tenant or user.tenant.type != 'subcity':
            return Response(
                {'error': 'Only subcity admins can review clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if user.role != 'subcity_admin':
            return Response(
                {'error': 'Only subcity admins can review clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if clearance is from a kebele under this subcity
        if clearance_request.source_kebele.parent != user.tenant:
            return Response(
                {'error': 'You can only review clearances from your kebeles'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if clearance can be reviewed
        if not clearance_request.can_be_approved and not clearance_request.can_be_rejected:
            return Response(
                {'error': 'This clearance request cannot be reviewed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(clearance_request, data=request.data)
        serializer.is_valid(raise_exception=True)
        
        action = serializer.validated_data['action']
        review_notes = serializer.validated_data.get('review_notes', '')
        
        with transaction.atomic():
            if action == 'approve':
                clearance_request.status = 'approved'
            else:  # reject
                clearance_request.status = 'rejected'
            
            clearance_request.reviewed_by = user
            clearance_request.reviewed_at = timezone.now()
            clearance_request.review_notes = review_notes
            clearance_request.save()
        
        return Response({
            'message': f'Clearance request {action}d successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data
        })
    
    @action(detail=True, methods=['post'])
    def issue(self, request, pk=None):
        """Issue clearance letter - Kebele leader only."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can issue clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can issue clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if this is the source kebele
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only issue clearance letters for your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Check if clearance can be issued
        if not clearance_request.can_be_issued:
            return Response(
                {'error': 'This clearance request cannot be issued (must be approved first)'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            # Generate clearance letter
            clearance_letter_path = self._generate_clearance_letter(clearance_request)

            clearance_request.status = 'issued'
            clearance_request.issued_by = user
            clearance_request.issued_at = timezone.now()
            clearance_request.clearance_letter_path = clearance_letter_path
            clearance_request.save()
        
        return Response({
            'message': 'Clearance letter issued successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data,
            'clearance_letter_path': clearance_letter_path
        })
    
    @action(detail=True, methods=['post'])
    def upload_documents(self, request, pk=None):
        """Upload required documents for clearance request."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only upload documents for your kebele clearances'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(clearance_request, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        with transaction.atomic():
            serializer.save(documents_uploaded_at=timezone.now())
        
        return Response({
            'message': 'Documents uploaded successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data
        })
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel clearance request."""
        clearance_request = self.get_object()
        user = request.user
        
        # Check permissions
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only cancel your kebele clearances'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if not clearance_request.can_be_cancelled:
            return Response(
                {'error': 'This clearance request cannot be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            clearance_request.status = 'cancelled'
            clearance_request.save()
        
        return Response({
            'message': 'Clearance request cancelled successfully',
            'clearance': CitizenClearanceRequestSerializer(clearance_request).data
        })
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get clearance statistics for the user's tenant."""
        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'pending': queryset.filter(status='pending').count(),
            'approved': queryset.filter(status='approved').count(),
            'rejected': queryset.filter(status='rejected').count(),
            'issued': queryset.filter(status='issued').count(),
            'cancelled': queryset.filter(status='cancelled').count(),
        }

        return Response(stats)

    @action(detail=False, methods=['post'])
    def generate(self, request):
        """Generate clearance letter directly without approval workflow."""
        user = request.user

        # Validate user has permission to generate clearance letters
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can generate clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can generate clearance letters'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate required data
        required_fields = ['citizen_id', 'citizen_name', 'destination_location', 'clearance_reason', 'reason_description']
        for field in required_fields:
            if not request.data.get(field):
                return Response(
                    {'error': f'{field.replace("_", " ").title()} is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        try:
            with transaction.atomic():
                # Create clearance record for tracking
                clearance_request = CitizenClearanceRequest.objects.create(
                    citizen_id=request.data['citizen_id'],
                    citizen_name=request.data['citizen_name'],
                    citizen_digital_id=request.data.get('citizen_digital_id', ''),
                    source_kebele=user.tenant,
                    destination_location=request.data['destination_location'],
                    clearance_reason=request.data['clearance_reason'],
                    reason_description=request.data['reason_description'],
                    status='issued',  # Skip approval workflow
                    requested_by=user,
                    reviewed_by=user,  # Auto-approved by issuer
                    issued_by=user,
                    reviewed_at=timezone.now(),
                    issued_at=timezone.now()
                )

                # Generate clearance letter
                clearance_letter_path = self._generate_clearance_letter(clearance_request)
                clearance_request.clearance_letter_path = clearance_letter_path
                clearance_request.save()

                # Return response with download URL
                from django.conf import settings
                clearance_letter_url = f"{settings.MEDIA_URL}{clearance_letter_path}"

                return Response({
                    'message': 'Clearance letter generated successfully',
                    'clearance_id': clearance_request.clearance_id,
                    'clearance_letter_path': clearance_letter_path,
                    'clearance_letter_url': clearance_letter_url,
                    'citizen_name': clearance_request.citizen_name,
                    'destination_location': clearance_request.destination_location
                })

        except Exception as e:
            return Response(
                {'error': f'Failed to generate clearance letter: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def auto_approve(self, request, pk=None):
        """Auto-approve clearance request and generate letter (simplified workflow)."""
        clearance_request = self.get_object()
        user = request.user

        # Check permissions
        if not user.tenant or user.tenant.type != 'kebele':
            return Response(
                {'error': 'Only kebele users can auto-approve clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        if user.role not in ['kebele_leader', 'clerk']:
            return Response(
                {'error': 'Only kebele leaders and clerks can auto-approve clearance requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if this is the source kebele
        if clearance_request.source_kebele != user.tenant:
            return Response(
                {'error': 'You can only auto-approve clearance requests for your kebele'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if clearance can be auto-approved
        if clearance_request.status != 'pending':
            return Response(
                {'error': 'This clearance request cannot be auto-approved'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                # Auto-approve and generate letter
                clearance_request.status = 'issued'
                clearance_request.reviewed_by = user
                clearance_request.issued_by = user
                clearance_request.reviewed_at = timezone.now()
                clearance_request.issued_at = timezone.now()
                clearance_request.review_notes = 'Auto-approved via simplified workflow'

                # Generate clearance letter
                clearance_letter_path = self._generate_clearance_letter(clearance_request)
                clearance_request.clearance_letter_path = clearance_letter_path
                clearance_request.save()

                # Return response with download URL
                from django.conf import settings
                clearance_letter_url = f"{settings.MEDIA_URL}{clearance_letter_path}"

                return Response({
                    'message': 'Clearance request auto-approved and letter generated successfully',
                    'clearance': CitizenClearanceRequestSerializer(clearance_request).data,
                    'clearance_letter_url': clearance_letter_url
                })

        except Exception as e:
            return Response(
                {'error': f'Failed to auto-approve clearance request: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_clearance_letter(self, clearance_request):
        """Generate clearance letter PDF (placeholder implementation)."""
        import os
        from django.conf import settings

        # Create clearance letters directory if it doesn't exist
        clearance_dir = os.path.join(settings.MEDIA_ROOT, 'clearance_letters')
        os.makedirs(clearance_dir, exist_ok=True)

        # Generate simple text-based clearance letter (placeholder)
        # In a real implementation, you would use a PDF library like reportlab
        letter_content = f"""
CITIZEN CLEARANCE LETTER

Clearance ID: {clearance_request.clearance_id}
Date: {timezone.now().strftime('%B %d, %Y')}

TO WHOM IT MAY CONCERN:

This is to certify that {clearance_request.citizen_name}
(Digital ID: {clearance_request.citizen_digital_id or 'N/A'})
has been cleared to relocate from {clearance_request.source_kebele.name}
to {clearance_request.destination_location}.

Reason for clearance: {clearance_request.clearance_reason_display}
Details: {clearance_request.reason_description}

This clearance was requested on {clearance_request.created_at.strftime('%B %d, %Y')}
and approved on {clearance_request.reviewed_at.strftime('%B %d, %Y') if clearance_request.reviewed_at else 'N/A'}.

The citizen is in good standing and has no pending obligations
with the local administration.

Issued by: {clearance_request.issued_by.first_name} {clearance_request.issued_by.last_name}
Position: {clearance_request.issued_by.role.replace('_', ' ').title()}
Date of Issue: {timezone.now().strftime('%B %d, %Y')}

Official Seal: [SEAL]

---
{clearance_request.source_kebele.name}
{clearance_request.source_kebele.parent.name if clearance_request.source_kebele.parent else ''}
        """

        # For now, save as text file (can be enhanced to PDF later)
        filename = f"{clearance_request.clearance_id}_clearance_letter.txt"
        file_path = os.path.join(clearance_dir, filename)

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(letter_content)

        # Return relative path for storage in database
        return f"clearance_letters/{filename}"

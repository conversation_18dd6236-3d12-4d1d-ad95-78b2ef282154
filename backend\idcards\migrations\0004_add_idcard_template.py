# Generated migration for IDCardTemplate model

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('idcards', '0003_alter_idcard_card_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='IDCardTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text="Template name (e.g., 'Default Template', 'Special Edition')", max_length=100)),
                ('description', models.TextField(blank=True, help_text='Template description')),
                ('background_color', models.CharField(default='#FFFFFF', help_text='Background color in hex format', max_length=7)),
                ('text_color', models.Char<PERSON>ield(default='#000000', help_text='Text color in hex format', max_length=7)),
                ('accent_color', models.<PERSON><PERSON><PERSON><PERSON>(default='#1976D2', help_text='Accent color in hex format', max_length=7)),
                ('logo_position', models.CharField(choices=[('top_left', 'Top Left'), ('top_center', 'Top Center'), ('top_right', 'Top Right')], default='top_left', max_length=20)),
                ('photo_position', models.CharField(choices=[('left', 'Left Side'), ('right', 'Right Side'), ('center', 'Center')], default='left', max_length=20)),
                ('header_text', models.CharField(default='Federal Democratic Republic of Ethiopia', max_length=200)),
                ('subtitle_text', models.CharField(default='National ID Card', max_length=200)),
                ('footer_text', models.CharField(blank=True, help_text='Optional footer text', max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False, help_text='Default template for this tenant')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='idcard',
            name='template',
            field=models.ForeignKey(blank=True, help_text='ID Card template to use', null=True, on_delete=django.db.models.deletion.SET_NULL, to='idcards.idcardtemplate'),
        ),
    ]

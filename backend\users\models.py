from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _


class UserRole(models.TextChoices):
    SUPERADMIN = 'superadmin', _('Super Admin')
    CITY_ADMIN = 'city_admin', _('City Admin')
    SUBCITY_ADMIN = 'subcity_admin', _('Sub City Admin')
    KEBELE_ADMIN = 'kebele_admin', _('Kebele Admin')
    KEBELE_LEADER = 'kebele_leader', _('Kebele Leader')
    CLERK = 'clerk', _('Clerk')


class User(AbstractUser):
    email = models.EmailField(_('email address'), unique=True)
    role = models.CharField(max_length=20, choices=UserRole.choices, default=UserRole.CLERK)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, null=True, blank=True)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def __str__(self):
        return self.email

    @property
    def is_superadmin(self):
        return self.role == UserRole.SUPERADMIN or self.is_superuser

    @property
    def is_city_admin(self):
        return self.role == UserRole.CITY_ADMIN

    @property
    def is_subcity_admin(self):
        return self.role == UserRole.SUBCITY_ADMIN

    @property
    def is_kebele_admin(self):
        return self.role == UserRole.KEBELE_ADMIN

    @property
    def is_kebele_leader(self):
        return self.role == UserRole.KEBELE_LEADER

    @property
    def is_clerk(self):
        return self.role == UserRole.CLERK

from django.db import models
from django_tenants.models import TenantMixin, DomainMixin


class TenantType(models.TextChoices):
    CITY = 'city', 'City'
    SUBCITY = 'subcity', 'Sub City'
    KEBELE = 'kebele', 'Kebele'


class Tenant(TenantMixin):
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=10, choices=TenantType.choices)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    created_on = models.DateTimeField(auto_now_add=True)
    
    # Default true, schema will be automatically created and synced when it is saved
    auto_create_schema = True
    
    def __str__(self):
        return self.name
    
    @property
    def is_city(self):
        return self.type == TenantType.CITY
    
    @property
    def is_subcity(self):
        return self.type == TenantType.SUBCITY
    
    @property
    def is_kebele(self):
        return self.type == TenantType.KEBELE
    
    @property
    def city(self):
        if self.is_city:
            return self
        elif self.is_subcity:
            return self.parent
        elif self.is_kebele:
            return self.parent.parent
        return None
    
    @property
    def subcity(self):
        if self.is_subcity:
            return self
        elif self.is_kebele:
            return self.parent
        return None


class Domain(DomainMixin):
    pass

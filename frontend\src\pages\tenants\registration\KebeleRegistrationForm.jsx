import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Divider,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { getStandardMenuProps, createSelectChangeHandler } from '../../../utils/dropdownUtils';
import axios from '../../../utils/axios';

const KebeleRegistrationForm = ({ data, onChange, parentOptions }) => {
  const [filteredSubcities, setFilteredSubcities] = useState([]);
  const [cities, setCities] = useState([]);
  const [selectedCity, setSelectedCity] = useState('');

  useEffect(() => {
    // Extract unique cities from parent options (subcities)
    const uniqueCities = [];
    const cityMap = {};

    parentOptions.forEach(subcity => {
      if (subcity.city_id && !cityMap[subcity.city_id]) {
        cityMap[subcity.city_id] = true;
        uniqueCities.push({
          id: subcity.city_id,
          name: subcity.city_name || `City ${subcity.city_id}`
        });
      }
    });

    setCities(uniqueCities);
  }, [parentOptions]);

  useEffect(() => {
    // Filter subcities based on selected city
    if (selectedCity) {
      const filtered = parentOptions.filter(subcity => subcity.city_id === selectedCity);
      setFilteredSubcities(filtered);
    } else {
      setFilteredSubcities(parentOptions);
    }
  }, [selectedCity, parentOptions]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange({ ...data, [name]: value });
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    onChange({ ...data, [name]: checked });
  };

  const handleCityChange = (e) => {
    const cityId = e.target.value;
    setSelectedCity(cityId);
    // Reset selected subcity when city changes
    onChange({ ...data, subcity_id: '' });
  };

  const handleDateChange = (date) => {
    onChange({ ...data, established_date: date });
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold">
        Kebele Information
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Enter the details for the kebele administration.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Basic Information</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="name"
            name="name"
            label="Kebele Name"
            value={data.name || ''}
            onChange={handleChange}
            placeholder="e.g., Kebele 01"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="tenant_name"
            name="tenant_name"
            label="Tenant Name"
            value={data.tenant_name || data.name || ''}
            onChange={handleChange}
            placeholder="e.g., Kebele 01 Administration"
            helperText="Name used for the tenant schema"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="domain_name"
            name="domain_name"
            label="Domain Name"
            value={data.domain_name || ''}
            onChange={handleChange}
            placeholder="e.g., kebele01.bole.addisababa.goid.gov.et"
            helperText="Domain name for tenant access (without http://)"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel id="city-filter-label">Filter by City</InputLabel>
            <Select
              labelId="city-filter-label"
              id="cityFilter"
              value={selectedCity}
              label="Filter by City"
              onChange={createSelectChangeHandler('cityFilter', handleCityChange)}
              MenuProps={getStandardMenuProps()}
            >
              <MenuItem value="">All Cities</MenuItem>
              {cities.map((city) => (
                <MenuItem key={city.id} value={city.id}>
                  {city.name}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Optional: Filter subcities by city</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth required>
            <InputLabel id="subcity-label">Parent SubCity</InputLabel>
            <Select
              labelId="subcity-label"
              id="subcity_id"
              name="subcity_id"
              value={data.subcity_id || data.parentId || ''}
              label="Parent SubCity"
              onChange={handleChange}
              MenuProps={getStandardMenuProps()}
            >
              {filteredSubcities.map((subcity) => (
                <MenuItem key={subcity.id} value={subcity.id}>
                  {subcity.name}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Select the subcity this kebele belongs to</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="code"
            name="code"
            label="Kebele Code"
            value={data.code || ''}
            onChange={handleChange}
            placeholder="e.g., KB-01"
            helperText="Leave blank for auto-generation"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Status</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={data.is_active !== false}
                onChange={handleSwitchChange}
                name="is_active"
                color="primary"
              />
            }
            label="Is Active"
          />
          <FormHelperText>Indicates whether the kebele is active</FormHelperText>
        </Grid>
      </Grid>
    </Box>
  );
};

export default KebeleRegistrationForm;

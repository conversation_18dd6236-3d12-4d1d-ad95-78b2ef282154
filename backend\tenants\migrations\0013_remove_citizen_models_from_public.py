# Generated manually to remove citizen models from public schema

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0012_remove_citizentransferrequest_completed_by_and_more'),
    ]

    operations = [
        # Remove citizen-related tables from public schema
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_citizen CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_emergencycontact CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_parent CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_child CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_spouse CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_document CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_biometric CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_photo CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_religion CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_citizenstatus CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_maritalstatus CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_documenttype CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_employmenttype CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_relationship CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS public.tenants_currentstatus CASCADE;",
            reverse_sql="-- Cannot reverse this operation"
        ),
    ]

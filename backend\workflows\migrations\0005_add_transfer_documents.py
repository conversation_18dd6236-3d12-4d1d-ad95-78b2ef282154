# Generated by Django 4.2.7 on 2025-06-03 16:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('workflows', '0004_make_citizen_id_nullable'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizentransferrequest',
            name='application_letter',
            field=models.FileField(blank=True, help_text='Application letter from citizen requesting transfer', null=True, upload_to='transfer_documents/application_letters/'),
        ),
        migrations.AddField(
            model_name='citizentransferrequest',
            name='current_kebele_id',
            field=models.FileField(blank=True, help_text='Current kebele ID card of the citizen', null=True, upload_to='transfer_documents/kebele_ids/'),
        ),
        migrations.AddField(
            model_name='citizentransferrequest',
            name='documents_uploaded_at',
            field=models.DateTimeField(blank=True, help_text='When documents were uploaded', null=True),
        ),
    ]

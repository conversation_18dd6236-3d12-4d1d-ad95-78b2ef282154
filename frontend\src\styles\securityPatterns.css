/**
 * Security Pattern Styles for ID Cards
 *
 * These styles apply security patterns to ID cards based on approval levels:
 * - Kebele approval: Left half castle pattern
 * - Subcity approval: Right half castle pattern
 * - Complete approval: Full castle pattern
 */

/* Base security pattern container */
.security-pattern-container {
  position: relative;
  overflow: hidden;
}

.security-pattern-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  opacity: 0.6; /* Much higher opacity for visibility */
}

/* Kebele approval pattern (left half) - Castle Watermark */
.security-pattern-kebele::before {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='castlePattern' patternUnits='userSpaceOnUse' width='400' height='300'%3E%3Cg fill='%23FFD700' opacity='0.35'%3E%3C!-- Main castle structure --%3E%3Crect x='50' y='150' width='300' height='120' fill='%23FFD700'/%3E%3C!-- Left tower --%3E%3Crect x='20' y='100' width='60' height='170' fill='%23FFD700'/%3E%3Crect x='25' y='95' width='10' height='15'/%3E%3Crect x='40' y='95' width='10' height='15'/%3E%3Crect x='55' y='95' width='10' height='15'/%3E%3Crect x='70' y='95' width='10' height='15'/%3E%3C!-- Right tower --%3E%3Crect x='320' y='80' width='60' height='190' fill='%23FFD700'/%3E%3Crect x='325' y='75' width='10' height='15'/%3E%3Crect x='340' y='75' width='10' height='15'/%3E%3Crect x='355' y='75' width='10' height='15'/%3E%3Crect x='370' y='75' width='10' height='15'/%3E%3C!-- Central keep --%3E%3Crect x='150' y='120' width='100' height='150' fill='%23FFD700'/%3E%3Crect x='155' y='115' width='15' height='15'/%3E%3Crect x='175' y='115' width='15' height='15'/%3E%3Crect x='195' y='115' width='15' height='15'/%3E%3Crect x='215' y='115' width='15' height='15'/%3E%3Crect x='235' y='115' width='15' height='15'/%3E%3C!-- Castle walls --%3E%3Crect x='80' y='160' width='70' height='110' fill='%23FFD700'/%3E%3Crect x='250' y='160' width='70' height='110' fill='%23FFD700'/%3E%3C!-- Battlements --%3E%3Crect x='85' y='155' width='10' height='15'/%3E%3Crect x='100' y='155' width='10' height='15'/%3E%3Crect x='115' y='155' width='10' height='15'/%3E%3Crect x='130' y='155' width='10' height='15'/%3E%3Crect x='255' y='155' width='10' height='15'/%3E%3Crect x='270' y='155' width='10' height='15'/%3E%3Crect x='285' y='155' width='10' height='15'/%3E%3Crect x='300' y='155' width='10' height='15'/%3E%3C!-- Windows and doors --%3E%3Crect x='35' y='180' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='35' y='220' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='160' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='240' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='175' y='180' width='25' height='40' fill='white' opacity='0.9'/%3E%3Crect x='175' y='230' width='25' height='40' fill='white' opacity='0.9'/%3E%3Crect x='100' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='280' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3C!-- Main gate --%3E%3Cpath d='M 180 250 Q 180 240 200 240 Q 220 240 220 250 L 220 270 L 180 270 Z' fill='white' opacity='1.0'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='400' height='300' fill='url(%23castlePattern)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}

/* Add visual border for kebele pattern */
.security-pattern-kebele {
  border-left: 5px solid #dfcd68 !important;
  box-shadow: inset 5px 0 10px rgba(204, 188, 95, 0.3) !important;
}

/* Subcity approval pattern (right half) - Castle Watermark */
.security-pattern-subcity::before {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='castlePatternRight' patternUnits='userSpaceOnUse' width='400' height='300'%3E%3Cg fill='%234CAF50' opacity='0.35'%3E%3C!-- Main castle structure --%3E%3Crect x='50' y='150' width='300' height='120' fill='%234CAF50'/%3E%3C!-- Left tower --%3E%3Crect x='20' y='100' width='60' height='170' fill='%234CAF50'/%3E%3Crect x='25' y='95' width='10' height='15'/%3E%3Crect x='40' y='95' width='10' height='15'/%3E%3Crect x='55' y='95' width='10' height='15'/%3E%3Crect x='70' y='95' width='10' height='15'/%3E%3C!-- Right tower --%3E%3Crect x='320' y='80' width='60' height='190' fill='%234CAF50'/%3E%3Crect x='325' y='75' width='10' height='15'/%3E%3Crect x='340' y='75' width='10' height='15'/%3E%3Crect x='355' y='75' width='10' height='15'/%3E%3Crect x='370' y='75' width='10' height='15'/%3E%3C!-- Central keep --%3E%3Crect x='150' y='120' width='100' height='150' fill='%234CAF50'/%3E%3Crect x='155' y='115' width='15' height='15'/%3E%3Crect x='175' y='115' width='15' height='15'/%3E%3Crect x='195' y='115' width='15' height='15'/%3E%3Crect x='215' y='115' width='15' height='15'/%3E%3Crect x='235' y='115' width='15' height='15'/%3E%3C!-- Castle walls --%3E%3Crect x='80' y='160' width='70' height='110' fill='%234CAF50'/%3E%3Crect x='250' y='160' width='70' height='110' fill='%234CAF50'/%3E%3C!-- Battlements --%3E%3Crect x='85' y='155' width='10' height='15'/%3E%3Crect x='100' y='155' width='10' height='15'/%3E%3Crect x='115' y='155' width='10' height='15'/%3E%3Crect x='130' y='155' width='10' height='15'/%3E%3Crect x='255' y='155' width='10' height='15'/%3E%3Crect x='270' y='155' width='10' height='15'/%3E%3Crect x='285' y='155' width='10' height='15'/%3E%3Crect x='300' y='155' width='10' height='15'/%3E%3C!-- Windows and doors --%3E%3Crect x='35' y='180' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='35' y='220' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='160' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='240' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='175' y='180' width='25' height='40' fill='white' opacity='0.9'/%3E%3Crect x='175' y='230' width='25' height='40' fill='white' opacity='0.9'/%3E%3Crect x='100' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='280' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3C!-- Main gate --%3E%3Cpath d='M 180 250 Q 180 240 200 240 Q 220 240 220 250 L 220 270 L 180 270 Z' fill='white' opacity='1.0'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='400' height='300' fill='url(%23castlePatternRight)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
}

/* Add visual border for subcity pattern */
.security-pattern-subcity {
  border-right: 5px solid #4CAF50 !important;
  box-shadow: inset -5px 0 10px rgba(76, 175, 80, 0.3) !important;
}

/* Complete approval pattern (full) - Complete Castle Watermark */
.security-pattern-complete::before {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='castlePatternComplete' patternUnits='userSpaceOnUse' width='400' height='300'%3E%3Cg fill='%23FFD700' opacity='0.4'%3E%3C!-- Main castle structure --%3E%3Crect x='50' y='150' width='300' height='120' fill='%23FFD700'/%3E%3C!-- Left tower --%3E%3Crect x='20' y='100' width='60' height='170' fill='%23FFD700'/%3E%3Crect x='25' y='95' width='10' height='15'/%3E%3Crect x='40' y='95' width='10' height='15'/%3E%3Crect x='55' y='95' width='10' height='15'/%3E%3Crect x='70' y='95' width='10' height='15'/%3E%3C!-- Right tower --%3E%3Crect x='320' y='80' width='60' height='190' fill='%234CAF50'/%3E%3Crect x='325' y='75' width='10' height='15'/%3E%3Crect x='340' y='75' width='10' height='15'/%3E%3Crect x='355' y='75' width='10' height='15'/%3E%3Crect x='370' y='75' width='10' height='15'/%3E%3C!-- Central keep --%3E%3Crect x='150' y='120' width='100' height='150' fill='%23FFD700'/%3E%3Crect x='155' y='115' width='15' height='15'/%3E%3Crect x='175' y='115' width='15' height='15'/%3E%3Crect x='195' y='115' width='15' height='15'/%3E%3Crect x='215' y='115' width='15' height='15'/%3E%3Crect x='235' y='115' width='15' height='15'/%3E%3C!-- Castle walls --%3E%3Crect x='80' y='160' width='70' height='110' fill='%23FFD700'/%3E%3Crect x='250' y='160' width='70' height='110' fill='%234CAF50'/%3E%3C!-- Battlements --%3E%3Crect x='85' y='155' width='10' height='15'/%3E%3Crect x='100' y='155' width='10' height='15'/%3E%3Crect x='115' y='155' width='10' height='15'/%3E%3Crect x='130' y='155' width='10' height='15'/%3E%3Crect x='255' y='155' width='10' height='15'/%3E%3Crect x='270' y='155' width='10' height='15'/%3E%3Crect x='285' y='155' width='10' height='15'/%3E%3Crect x='300' y='155' width='10' height='15'/%3E%3C!-- Windows and doors --%3E%3Crect x='35' y='180' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='35' y='220' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='160' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='340' y='240' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='175' y='180' width='25' height='40' fill='white' opacity='0.9'/%3E%3Crect x='175' y='230' width='25' height='40' fill='white' opacity='0.9'/%3E%3Crect x='100' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3Crect x='280' y='200' width='20' height='30' fill='white' opacity='0.9'/%3E%3C!-- Main gate --%3E%3Cpath d='M 180 250 Q 180 240 200 240 Q 220 240 220 250 L 220 270 L 180 270 Z' fill='white' opacity='1.0'/%3E%3C!-- Crown for complete approval --%3E%3Ccircle cx='200' cy='50' r='15' fill='%23FFD700' opacity='1.0'/%3E%3Cpolygon points='200,35 205,45 215,45 207,52 210,62 200,57 190,62 193,52 185,45 195,45' fill='white' opacity='1.0'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='400' height='300' fill='url(%23castlePatternComplete)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* Add visual border for complete pattern */
.security-pattern-complete {
  border: 5px solid #4CAF50 !important;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5), inset 0 0 15px rgba(255, 215, 0, 0.3) !important;
}

/* Pattern overlay for better visibility */
.security-pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 215, 0, 0.1) 50%, transparent 60%);
  pointer-events: none;
  z-index: 2;
}

/* Status indicators - Much more prominent */
.pattern-status-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  z-index: 3;
  border: 2px solid;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.pattern-status-kebele {
  background: #FFD700;
  color: #333;
  border-color: #FFA000;
}

.pattern-status-subcity {
  background: #4CAF50;
  color: white;
  border-color: #2E7D32;
}

.pattern-status-complete {
  background: linear-gradient(45deg, #FFD700, #4CAF50);
  color: white;
  border-color: #2E7D32;
  animation: pulse 2s infinite;
}

/* Pulse animation for complete pattern */
@keyframes pulse {
  0% { box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
  50% { box-shadow: 0 4px 16px rgba(76, 175, 80, 0.6); }
  100% { box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
}

/* Animation for pattern application */
@keyframes patternApply {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.15;
    transform: scale(1);
  }
}

.security-pattern-container.pattern-applying::before {
  animation: patternApply 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .security-pattern-container::before {
    background-size: 150px 112px;
  }

  .pattern-status-indicator {
    font-size: 8px;
    padding: 2px 6px;
  }
}

/* Large watermark for pattern status */
.security-pattern-kebele::after {
  content: '🏛️ KEBELE APPROVED';
  position: absolute;
  top: 50%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(-15deg);
  font-size: 24px;
  font-weight: bold;
  color: rgba(255, 215, 0, 0.8);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  z-index: 2;
  pointer-events: none;
}

.security-pattern-subcity::after {
  content: '🏢 SUBCITY APPROVED';
  position: absolute;
  top: 50%;
  right: 25%;
  transform: translate(50%, -50%) rotate(15deg);
  font-size: 24px;
  font-weight: bold;
  color: rgba(76, 175, 80, 0.8);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  z-index: 2;
  pointer-events: none;
}

/* Removed "FULLY SECURED" text overlay for cleaner design */

/* Glow animation for complete pattern */
@keyframes glow {
  from { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 10px rgba(76, 175, 80, 0.5); }
  to { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 20px rgba(76, 175, 80, 0.8); }
}

/* Print styles */
@media print {
  .security-pattern-container::before {
    opacity: 0.1;
  }

  .pattern-status-indicator {
    display: none;
  }

  /* Text overlays removed for cleaner design */
}

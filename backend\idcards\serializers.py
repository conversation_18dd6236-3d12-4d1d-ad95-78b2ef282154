from rest_framework import serializers
from .models import IDCard, IDCardTemplate
from tenants.serializers.citizen_serializer import TenantCitizenSerializer


class IDCardTemplateSerializer(serializers.ModelSerializer):
    """Serializer for ID Card Templates"""

    class Meta:
        model = IDCardTemplate
        fields = (
            'id', 'name', 'description', 'background_color', 'text_color', 'accent_color',
            'logo_position', 'photo_position', 'header_text', 'subtitle_text', 'footer_text',
            'is_active', 'is_default', 'created_at', 'updated_at'
        )
        read_only_fields = ('created_at', 'updated_at')


class IDCardSerializer(serializers.ModelSerializer):
    citizen_name = serializers.CharField(source='citizen.get_full_name', read_only=True)
    citizen_details = TenantCitizenSerializer(source='citizen', read_only=True)
    template_details = IDCardTemplateSerializer(source='template', read_only=True)
    created_by_username = serializers.Char<PERSON><PERSON>(source='created_by.username', read_only=True)
    approved_by_username = serializers.Char<PERSON><PERSON>(source='approved_by.username', read_only=True)
    issued_by_username = serializers.Char<PERSON>ield(source='issued_by.username', read_only=True)
    printed_by_username = serializers.Char<PERSON>ield(source='printed_by.username', read_only=True)

    class Meta:
        model = IDCard
        fields = (
            'id', 'card_number', 'citizen', 'citizen_name', 'citizen_details',
            'template', 'template_details', 'issue_date', 'expiry_date', 'status', 'qr_code', 'uuid',
            'approval_pattern', 'approval_comment', 'submitted_for_approval_at', 'approved_at',
            'has_kebele_pattern', 'has_subcity_pattern',  # Added security pattern fields
            'created_by', 'created_by_username', 'approved_by', 'approved_by_username',
            'issued_by', 'issued_by_username', 'printed_by', 'printed_by_username', 'printed_at',
            'created_at', 'updated_at', 'pdf_file'
        )
        read_only_fields = (
            'card_number', 'qr_code', 'uuid', 'created_by', 'approved_by',
            'issued_by', 'printed_by', 'printed_at', 'created_at', 'updated_at', 'pdf_file', 'submitted_for_approval_at', 'approved_at'
        )

    def create(self, validated_data):
        """Set the created_by field to the current user."""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['created_by'] = request.user
        return super().create(validated_data)


class IDCardListSerializer(serializers.ModelSerializer):
    """Simplified serializer for list views."""
    citizen_name = serializers.CharField(source='citizen.get_full_name', read_only=True)

    class Meta:
        model = IDCard
        fields = (
            'id', 'card_number', 'citizen', 'citizen_name',
            'issue_date', 'expiry_date', 'status', 'created_at',
            'has_kebele_pattern', 'has_subcity_pattern'  # Added security pattern fields
        )


class IDCardStatusUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating ID card status."""
    comment = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = IDCard
        fields = ('status', 'comment')


class IDCardApprovalSerializer(serializers.ModelSerializer):
    """Serializer for ID card approval actions."""
    action = serializers.ChoiceField(
        choices=[
            'submit_for_approval',
            'kebele_approve',
            'subcity_approve',
            'reject'
        ],
        write_only=True
    )
    comment = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = IDCard
        fields = ('action', 'comment')

    def validate(self, data):
        action = data.get('action')

        # If approving, require approval pattern
        if action == 'approve' and not data.get('approval_pattern'):
            raise serializers.ValidationError({
                'approval_pattern': 'Approval pattern is required when approving an ID card.'
            })

        return data

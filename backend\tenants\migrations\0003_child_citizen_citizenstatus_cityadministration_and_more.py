# Generated by Django 4.2.7 on 2025-05-22 22:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0002_country_subcity_region_kebele_city'),
    ]

    operations = [
        migrations.CreateModel(
            name='Child',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('date_of_birth', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Citizen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('digital_id', models.CharField(max_length=50, unique=True)),
            ],
            options={
                'verbose_name': 'Citizen',
                'verbose_name_plural': 'Citizens',
            },
        ),
        migrations.CreateModel(
            name='CitizenStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Citizen Status',
                'verbose_name_plural': 'Citizen Statuses',
            },
        ),
        migrations.CreateModel(
            name='CityAdministration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('city_code', models.CharField(max_length=10, unique=True)),
                ('city_name', models.CharField(max_length=100)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='logos/')),
                ('motto_slogan', models.TextField(blank=True, null=True)),
                ('city_intro', models.TextField(blank=True, null=True)),
                ('mayor_name', models.CharField(blank=True, max_length=100, null=True)),
                ('deputy_mayor', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('google_maps_url', models.URLField(blank=True, null=True)),
                ('area_sq_km', models.FloatField(blank=True, null=True)),
                ('elevation_meters', models.IntegerField(blank=True, null=True)),
                ('timezone', models.CharField(default='EAT', max_length=50)),
                ('website', models.URLField(blank=True, null=True)),
                ('headquarter_address', models.TextField(blank=True, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('established_date', models.DateField(blank=True, null=True)),
                ('is_resident', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'City Administration',
                'verbose_name_plural': 'City Administrations',
                'ordering': ['city_name', 'city_code'],
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_file', models.FileField(upload_to='citizen_documents/')),
                ('issue_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Document',
                'verbose_name_plural': 'Documents',
            },
        ),
        migrations.CreateModel(
            name='DocumentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Document Type',
                'verbose_name_plural': 'Document Types',
            },
        ),
        migrations.CreateModel(
            name='EmergencyContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('relationship', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_contacts', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Emergency Contact',
                'verbose_name_plural': 'Emergency Contacts',
            },
        ),
        migrations.CreateModel(
            name='EmploymentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Employment Type',
                'verbose_name_plural': 'Employment Types',
            },
        ),
        migrations.CreateModel(
            name='Ketena',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(blank=True, max_length=10, null=True, unique=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Ketena',
                'verbose_name_plural': 'Ketenes',
            },
        ),
        migrations.CreateModel(
            name='MaritalStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=50)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Marital Status',
                'verbose_name_plural': 'Marital Statuses',
            },
        ),
        migrations.CreateModel(
            name='Parent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('first_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name_am', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Religion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Religion',
                'verbose_name_plural': 'Religions',
            },
        ),
        migrations.CreateModel(
            name='Spouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_name', models.CharField(max_length=100)),
                ('middle_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('primary_contact', models.BooleanField(default=False, help_text='Indicates whether this is the primary emergency contact.')),
                ('is_active', models.BooleanField(default=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spouse', to='tenants.citizen')),
            ],
            options={
                'verbose_name': 'Spouse',
                'verbose_name_plural': 'Spouses',
            },
        ),
        migrations.AlterModelOptions(
            name='country',
            options={'ordering': ['name', 'code'], 'verbose_name': 'Country', 'verbose_name_plural': 'Countries'},
        ),
        migrations.AlterModelOptions(
            name='kebele',
            options={'verbose_name': 'Kebele', 'verbose_name_plural': 'Kebeles'},
        ),
        migrations.AlterModelOptions(
            name='region',
            options={'ordering': ['name', 'code'], 'verbose_name': 'Region', 'verbose_name_plural': 'Regions'},
        ),
        migrations.AlterModelOptions(
            name='subcity',
            options={'verbose_name': 'SubCity', 'verbose_name_plural': 'SubCities'},
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='area_sq_km',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='chairman_name',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='contact_email',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='contact_phone',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='established_date',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='google_maps_url',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='kebele_code',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='kebele_intro',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='kebele_name',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='logo',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='manager_name',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='office_address',
        ),
        migrations.RemoveField(
            model_name='kebele',
            name='population',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='administrator_name',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='area_sq_km',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='contact_email',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='contact_phone',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='deputy_administrator',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='established_date',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='google_maps_url',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='logo',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='motto_slogan',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='office_address',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='population',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='postal_code',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='subcity_code',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='subcity_intro',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='subcity_name',
        ),
        migrations.RemoveField(
            model_name='subcity',
            name='website',
        ),
        migrations.AddField(
            model_name='country',
            name='capital_city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='kebele',
            name='code',
            field=models.CharField(blank=True, max_length=10, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='kebele',
            name='name',
            field=models.CharField(default='Kebele', max_length=100),
        ),
        migrations.AddField(
            model_name='kebele',
            name='sub_city',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='kebeles', to='tenants.subcity'),
        ),
        migrations.AddField(
            model_name='subcity',
            name='code',
            field=models.CharField(blank=True, max_length=10, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='subcity',
            name='name',
            field=models.CharField(default='Subcity', max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='region',
            name='code',
            field=models.CharField(max_length=5, unique=True),
        ),
        migrations.DeleteModel(
            name='City',
        ),
        migrations.AddField(
            model_name='spouse',
            name='nationality',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='spouse_nationality', to='tenants.country'),
        ),
        migrations.AddField(
            model_name='parent',
            name='nationality',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.country'),
        ),
        migrations.AddField(
            model_name='ketena',
            name='kebele',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ketenes', to='tenants.kebele'),
        ),
        migrations.AddField(
            model_name='emergencycontact',
            name='nationality',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='emergency_contact_nationality', to='tenants.country'),
        ),
        migrations.AddField(
            model_name='document',
            name='document_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tenants.documenttype'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.country'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.region'),
        ),
        migrations.AddField(
            model_name='cityadministration',
            name='tenant',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='city_profile', to='tenants.tenant'),
        ),
        migrations.AddField(
            model_name='child',
            name='citizen',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='children', to='tenants.citizen'),
        ),
        migrations.AddField(
            model_name='child',
            name='nationality',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tenants.country'),
        ),
        migrations.AddField(
            model_name='subcity',
            name='city',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcities', to='tenants.cityadministration'),
        ),
        migrations.AlterUniqueTogether(
            name='spouse',
            unique_together={('citizen', 'phone')},
        ),
        migrations.AlterUniqueTogether(
            name='emergencycontact',
            unique_together={('citizen', 'phone')},
        ),
    ]

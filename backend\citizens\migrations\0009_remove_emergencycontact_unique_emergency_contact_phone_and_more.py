# Generated by Django 4.2.7 on 2025-05-26 11:44

import citizens.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0008_add_digital_id_constraint'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='emergencycontact',
            name='unique_emergency_contact_phone',
        ),
        migrations.RemoveConstraint(
            model_name='spouse',
            name='unique_spouse_phone',
        ),
        migrations.AlterField(
            model_name='child',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='gender',
            field=models.Char<PERSON>ield(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='is_resident',
            field=models.Bo<PERSON>an<PERSON>ield(default=True, help_text='Indicates whether the citizen is a resident of the city.'),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='parent',
            name='gender',
            field=models.Char<PERSON><PERSON>(choices=[('male', 'Male'), ('female', 'Female')], max_length=10),
        ),
        migrations.AlterField(
            model_name='photo',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to=citizens.models.citizen_photo_path),
        ),
        migrations.AlterUniqueTogether(
            name='emergencycontact',
            unique_together={('citizen', 'phone')},
        ),
        migrations.AlterUniqueTogether(
            name='spouse',
            unique_together={('citizen', 'phone')},
        ),
    ]

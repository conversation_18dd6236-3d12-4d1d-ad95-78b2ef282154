import os
from django.core.management.base import BaseCommand
from shared.models import (
    Country, 
    Region, 
    Religion, 
    CitizenStatus, 
    MaritalStatus, 
    DocumentType, 
    EmploymentType, 
    Relationship, 
    CurrentStatus, 
    BiometricType,
    Ketena
)

class Command(BaseCommand):
    help = 'Populates shared models with initial data for dropdowns'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Starting to populate shared data...'))
        
        # Create countries
        self.populate_countries()
        
        # Create regions
        self.populate_regions()
        
        # Create religions
        self.populate_religions()
        
        # Create citizen statuses
        self.populate_citizen_statuses()
        
        # Create marital statuses
        self.populate_marital_statuses()
        
        # Create document types
        self.populate_document_types()
        
        # Create employment types
        self.populate_employment_types()
        
        # Create relationships
        self.populate_relationships()
        
        # Create current statuses
        self.populate_current_statuses()
        
        # Create biometric types
        self.populate_biometric_types()
        
        # Create ketenas
        self.populate_ketenas()
        
        self.stdout.write(self.style.SUCCESS('Successfully populated shared data!'))
    
    def populate_countries(self):
        countries = [
            {'name': 'Ethiopia', 'code': 'ETH', 'capital_city': 'Addis Ababa'},
            {'name': 'Kenya', 'code': 'KEN', 'capital_city': 'Nairobi'},
            {'name': 'Sudan', 'code': 'SDN', 'capital_city': 'Khartoum'},
            {'name': 'South Sudan', 'code': 'SSD', 'capital_city': 'Juba'},
            {'name': 'Somalia', 'code': 'SOM', 'capital_city': 'Mogadishu'},
            {'name': 'Djibouti', 'code': 'DJI', 'capital_city': 'Djibouti'},
            {'name': 'Eritrea', 'code': 'ERI', 'capital_city': 'Asmara'},
        ]
        
        for country_data in countries:
            Country.objects.get_or_create(
                code=country_data['code'],
                defaults={
                    'name': country_data['name'],
                    'capital_city': country_data['capital_city'],
                }
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(countries)} countries'))
    
    def populate_regions(self):
        # Get Ethiopia
        try:
            ethiopia = Country.objects.get(code='ETH')
            
            regions = [
                {'name': 'Addis Ababa', 'code': 'AA'},
                {'name': 'Afar', 'code': 'AF'},
                {'name': 'Amhara', 'code': 'AM'},
                {'name': 'Benishangul-Gumuz', 'code': 'BG'},
                {'name': 'Dire Dawa', 'code': 'DD'},
                {'name': 'Gambela', 'code': 'GM'},
                {'name': 'Harari', 'code': 'HR'},
                {'name': 'Oromia', 'code': 'OR'},
                {'name': 'Sidama', 'code': 'SD'},
                {'name': 'Somali', 'code': 'SM'},
                {'name': 'South West Ethiopia', 'code': 'SW'},
                {'name': 'Southern Nations, Nationalities, and Peoples', 'code': 'SN'},
                {'name': 'Tigray', 'code': 'TG'},
            ]
            
            for region_data in regions:
                Region.objects.get_or_create(
                    code=region_data['code'],
                    defaults={
                        'name': region_data['name'],
                        'country': ethiopia,
                    }
                )
            
            self.stdout.write(self.style.SUCCESS(f'Created {len(regions)} regions for Ethiopia'))
        except Country.DoesNotExist:
            self.stdout.write(self.style.WARNING('Ethiopia not found, skipping regions'))
    
    def populate_religions(self):
        religions = [
            {'name': 'Orthodox Christianity'},
            {'name': 'Islam'},
            {'name': 'Protestantism'},
            {'name': 'Catholicism'},
            {'name': 'Traditional Beliefs'},
            {'name': 'Judaism'},
            {'name': 'Hinduism'},
            {'name': 'Buddhism'},
            {'name': 'Other'},
            {'name': 'None'},
        ]
        
        for religion_data in religions:
            Religion.objects.get_or_create(name=religion_data['name'])
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(religions)} religions'))
    
    def populate_citizen_statuses(self):
        statuses = [
            {'name': 'Citizen', 'description': 'Full citizen of the country'},
            {'name': 'Resident', 'description': 'Legal resident but not a citizen'},
            {'name': 'Refugee', 'description': 'Person granted refugee status'},
            {'name': 'Visitor', 'description': 'Temporary visitor'},
            {'name': 'Foreign National', 'description': 'Citizen of another country'},
            {'name': 'Stateless', 'description': 'Person without citizenship of any country'},
        ]
        
        for status_data in statuses:
            CitizenStatus.objects.get_or_create(
                name=status_data['name'],
                defaults={'description': status_data.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(statuses)} citizen statuses'))
    
    def populate_marital_statuses(self):
        statuses = [
            {'name': 'Single', 'description': 'Never married'},
            {'name': 'Married', 'description': 'Currently married'},
            {'name': 'Divorced', 'description': 'Legally divorced'},
            {'name': 'Widowed', 'description': 'Spouse is deceased'},
            {'name': 'Separated', 'description': 'Legally separated but not divorced'},
        ]
        
        for status_data in statuses:
            MaritalStatus.objects.get_or_create(
                name=status_data['name'],
                defaults={'description': status_data.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(statuses)} marital statuses'))
    
    def populate_document_types(self):
        doc_types = [
            {'name': 'National ID Card', 'description': 'Official national identification card'},
            {'name': 'Birth Certificate', 'description': 'Official birth certificate'},
            {'name': 'Marriage Certificate', 'description': 'Official marriage certificate'},
            {'name': 'Passport', 'description': 'International travel document'},
            {'name': 'Driver License', 'description': 'Official driving permit'},
            {'name': 'Residence Permit', 'description': 'Document proving legal residence'},
            {'name': 'Voter ID', 'description': 'Identification for voting purposes'},
            {'name': 'Tax ID', 'description': 'Tax identification document'},
            {'name': 'Social Security Card', 'description': 'Social security identification'},
            {'name': 'Military ID', 'description': 'Military service identification'},
        ]
        
        for doc_type in doc_types:
            DocumentType.objects.get_or_create(
                name=doc_type['name'],
                defaults={'description': doc_type.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(doc_types)} document types'))
    
    def populate_employment_types(self):
        emp_types = [
            {'name': 'Full-time', 'description': 'Regular full-time employment'},
            {'name': 'Part-time', 'description': 'Regular part-time employment'},
            {'name': 'Self-employed', 'description': 'Working for oneself'},
            {'name': 'Unemployed', 'description': 'Not currently employed'},
            {'name': 'Student', 'description': 'Full-time student'},
            {'name': 'Retired', 'description': 'Retired from work'},
            {'name': 'Government Employee', 'description': 'Employed by the government'},
            {'name': 'Private Sector', 'description': 'Employed in the private sector'},
            {'name': 'NGO Employee', 'description': 'Employed by a non-governmental organization'},
            {'name': 'Casual Worker', 'description': 'Irregular or casual employment'},
        ]
        
        for emp_type in emp_types:
            EmploymentType.objects.get_or_create(
                name=emp_type['name'],
                defaults={'description': emp_type.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(emp_types)} employment types'))
    
    def populate_relationships(self):
        relationships = [
            {'name': 'Parent', 'description': 'Parent relationship'},
            {'name': 'Child', 'description': 'Child relationship'},
            {'name': 'Spouse', 'description': 'Married partner'},
            {'name': 'Sibling', 'description': 'Brother or sister'},
            {'name': 'Grandparent', 'description': 'Parent of a parent'},
            {'name': 'Grandchild', 'description': 'Child of a child'},
            {'name': 'Uncle/Aunt', 'description': 'Sibling of a parent'},
            {'name': 'Nephew/Niece', 'description': 'Child of a sibling'},
            {'name': 'Cousin', 'description': 'Child of an uncle or aunt'},
            {'name': 'Friend', 'description': 'Non-family relationship'},
            {'name': 'Guardian', 'description': 'Legal guardian'},
            {'name': 'Other', 'description': 'Other relationship type'},
        ]
        
        for rel in relationships:
            Relationship.objects.get_or_create(
                name=rel['name'],
                defaults={'description': rel.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(relationships)} relationships'))
    
    def populate_current_statuses(self):
        statuses = [
            {'name': 'Active', 'description': 'Currently active'},
            {'name': 'Deceased', 'description': 'Person is deceased'},
            {'name': 'Relocated', 'description': 'Moved to another location'},
            {'name': 'Transferred', 'description': 'Transferred to another jurisdiction'},
            {'name': 'Suspended', 'description': 'Temporarily suspended'},
            {'name': 'Inactive', 'description': 'Currently inactive'},
        ]
        
        for status in statuses:
            CurrentStatus.objects.get_or_create(
                name=status['name'],
                defaults={'description': status.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(statuses)} current statuses'))
    
    def populate_biometric_types(self):
        bio_types = [
            {'name': 'Fingerprint', 'description': 'Fingerprint biometric data'},
            {'name': 'Facial Recognition', 'description': 'Facial recognition biometric data'},
            {'name': 'Iris Scan', 'description': 'Iris scan biometric data'},
            {'name': 'Voice Recognition', 'description': 'Voice recognition biometric data'},
            {'name': 'Hand Geometry', 'description': 'Hand geometry biometric data'},
            {'name': 'Signature', 'description': 'Signature biometric data'},
        ]
        
        for bio_type in bio_types:
            BiometricType.objects.get_or_create(
                name=bio_type['name'],
                defaults={'description': bio_type.get('description', '')}
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(bio_types)} biometric types'))
    
    def populate_ketenas(self):
        ketenas = [
            {'name': 'Ketena 01', 'code': 'KT01', 'description': 'Ketena 01 description'},
            {'name': 'Ketena 02', 'code': 'KT02', 'description': 'Ketena 02 description'},
            {'name': 'Ketena 03', 'code': 'KT03', 'description': 'Ketena 03 description'},
            {'name': 'Ketena 04', 'code': 'KT04', 'description': 'Ketena 04 description'},
            {'name': 'Ketena 05', 'code': 'KT05', 'description': 'Ketena 05 description'},
        ]
        
        for ketena in ketenas:
            Ketena.objects.get_or_create(
                code=ketena['code'],
                defaults={
                    'name': ketena['name'],
                    'description': ketena.get('description', '')
                }
            )
        
        self.stdout.write(self.style.SUCCESS(f'Created {len(ketenas)} ketenas'))

from django.db import models
from django.conf import settings as django_settings
from ..utils import Timestamp
from .city import CityAdministration

class SubCity(Timestamp):
    # SubCity model to represent a sub-city area
    city = models.ForeignKey(CityAdministration, on_delete=models.CASCADE, related_name='subcities', null=True)  # Relating to the City model
    name = models.CharField(max_length=100, unique=True, default="Subcity")
    code = models.CharField(max_length=10, unique=True, blank=True, null=True)
    is_active = models.BooleanField(default=True)  # Boolean field for activity status
    tenant = models.OneToOneField('tenants.Tenant', on_delete=models.CASCADE, related_name='subcity_profile')

    # Auto-generated code logic, can be done via override of save method if needed
    def save(self, *args, **kwargs):
        # Generate code only if it doesn't exist
        if not self.code:
            if not self.id:
                # For new instances, save first to get ID, then update code
                super().save(*args, **kwargs)
                self.code = f"GD-SC{self.id:02d}"  # Generates code like GD-SC01, GD-SC02, etc.
                # Update only the code field to avoid ID conflicts
                super().save(update_fields=['code'])
                return
            else:
                # For existing instances, just set the code
                self.code = f"GD-SC{self.id:02d}"

        # Normal save for all other cases
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "SubCity"
        verbose_name_plural = "SubCities"

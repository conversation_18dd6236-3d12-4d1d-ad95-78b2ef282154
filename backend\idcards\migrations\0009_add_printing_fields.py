# Generated by Django 4.2.7 on 2025-05-29 17:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('idcards', '0008_rename_approval_comment_idcard_kebele_approval_comment_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='idcard',
            name='printed_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was printed', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='printed_by',
            field=models.ForeignKey(blank=True, help_text='User who printed the ID card', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='printed_id_cards', to=settings.AUTH_USER_MODEL),
        ),
    ]

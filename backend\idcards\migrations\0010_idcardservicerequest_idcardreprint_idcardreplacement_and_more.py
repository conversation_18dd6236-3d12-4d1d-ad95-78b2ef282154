# Generated by Django 4.2.7 on 2025-06-06 15:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0014_citizen_deactivated_at_citizen_deactivation_reason'),
        ('idcards', '0009_add_printing_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='IDCardServiceRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_type', models.CharField(choices=[('renew', 'Renewal'), ('replacement', 'Replacement'), ('reprint', 'Reprint')], max_length=20)),
                ('application_method', models.CharField(choices=[('online', 'Online Application'), ('physical', 'Physical Visit')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('kebele_approved', 'Approved by <PERSON><PERSON><PERSON>'), ('subcity_processing', 'Processing at SubCity'), ('printing', 'Printing'), ('ready', 'Ready for Collection'), ('completed', 'Completed'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('reason', models.TextField()),
                ('supporting_documents', models.JSONField(blank=True, default=list)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('kebele_reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('subcity_forwarded_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('kebele_reviewer', models.CharField(blank=True, max_length=100, null=True)),
                ('subcity_processor', models.CharField(blank=True, max_length=100, null=True)),
                ('kebele_notes', models.TextField(blank=True, null=True)),
                ('subcity_notes', models.TextField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('citizen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_requests', to='citizens.citizen')),
            ],
            options={
                'verbose_name': 'ID Card Service Request',
                'verbose_name_plural': 'ID Card Service Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='IDCardReprint',
            fields=[
                ('idcardservicerequest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='idcards.idcardservicerequest')),
                ('police_report', models.FileField(blank=True, null=True, upload_to='police_reports/')),
                ('police_report_number', models.CharField(max_length=100)),
                ('loss_date', models.DateField()),
                ('loss_location', models.CharField(max_length=200)),
                ('old_id_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reprint_requests', to='idcards.idcard')),
            ],
            options={
                'verbose_name': 'ID Card Reprint',
                'verbose_name_plural': 'ID Card Reprints',
            },
            bases=('idcards.idcardservicerequest',),
        ),
        migrations.CreateModel(
            name='IDCardReplacement',
            fields=[
                ('idcardservicerequest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='idcards.idcardservicerequest')),
                ('damage_description', models.TextField()),
                ('damage_photos', models.JSONField(blank=True, default=list)),
                ('old_id_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='replacement_requests', to='idcards.idcard')),
            ],
            options={
                'verbose_name': 'ID Card Replacement',
                'verbose_name_plural': 'ID Card Replacements',
            },
            bases=('idcards.idcardservicerequest',),
        ),
        migrations.CreateModel(
            name='IDCardRenewal',
            fields=[
                ('idcardservicerequest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='idcards.idcardservicerequest')),
                ('new_expiry_date', models.DateField()),
                ('old_id_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='renewal_requests', to='idcards.idcard')),
            ],
            options={
                'verbose_name': 'ID Card Renewal',
                'verbose_name_plural': 'ID Card Renewals',
            },
            bases=('idcards.idcardservicerequest',),
        ),
    ]

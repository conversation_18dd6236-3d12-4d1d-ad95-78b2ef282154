# Generated manually for removing old fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0004_convert_data'),
    ]

    operations = [
        # Remove old fields
        migrations.RemoveField(
            model_name='citizen',
            name='blood_type',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='emergency_contact_name',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='emergency_contact_phone',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='emergency_contact_relation',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='phone_number',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='street',
        ),
        migrations.RemoveField(
            model_name='citizen',
            name='occupation',
        ),

        # Replace marital_status field
        migrations.RemoveField(
            model_name='citizen',
            name='marital_status',
        ),
        migrations.RenameField(
            model_name='citizen',
            old_name='marital_status_new',
            new_name='marital_status',
        ),

        # Replace nationality field
        migrations.RemoveField(
            model_name='citizen',
            name='nationality',
        ),
        migrations.RenameField(
            model_name='citizen',
            old_name='nationality_new',
            new_name='nationality',
        ),

        # Update field types and constraints
        migrations.AlterField(
            model_name='citizen',
            name='date_of_birth',
            field=models.DateField(null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='gender',
            field=models.CharField(max_length=10, blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='middle_name',
            field=models.CharField(max_length=100, blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='citizen',
            name='photo',
            field=models.TextField(blank=True, null=True),
        ),

        # Update Meta options
        migrations.AlterModelOptions(
            name='citizen',
            options={'ordering': ['-created_at'], 'verbose_name': 'Citizen', 'verbose_name_plural': 'Citizens'},
        ),
    ]

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from ..models.city import CityAdministration, Region, Country
from ..models.subcity import SubCity
from ..models.kebele import Kebel<PERSON>, Ketena
from ..models.tenant import Tenant, TenantType
from ..serializers import (
    CityAdministrationSerializer, CityRegistrationSerializer,
    SubCitySerializer, SubCityRegistrationSerializer,
    KebeleSerializer, KebeleRegistrationSerializer,
    KetenaSerializer, KetenaCreateSerializer,
    CountrySerializer, RegionSerializer
)
from ..permissions import IsSuperUser, CanManageTenants, CanRegisterTenants

class CountryViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for retrieving countries."""
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    permission_classes = [permissions.IsAuthenticated]

class RegionViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for retrieving regions."""
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['country']

class CityViewSet(viewsets.ModelViewSet):
    """API endpoint for managing cities."""
    queryset = CityAdministration.objects.all()
    serializer_class = CityAdministrationSerializer
    permission_classes = [permissions.IsAuthenticated, CanRegisterTenants]

    def get_serializer_class(self):
        if self.action == 'create':
            return CityRegistrationSerializer
        return CityAdministrationSerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class SubcityViewSet(viewsets.ModelViewSet):
    """API endpoint for managing subcities."""
    queryset = SubCity.objects.all()
    serializer_class = SubCitySerializer
    permission_classes = [permissions.IsAuthenticated, CanRegisterTenants]
    filterset_fields = ['city']

    def get_serializer_class(self):
        if self.action == 'create':
            return SubCityRegistrationSerializer
        return SubCitySerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class KebeleViewSet(viewsets.ModelViewSet):
    """API endpoint for managing kebeles."""
    queryset = Kebele.objects.all()
    serializer_class = KebeleSerializer
    permission_classes = [permissions.IsAuthenticated, CanRegisterTenants]
    filterset_fields = ['sub_city']

    def get_serializer_class(self):
        if self.action == 'create':
            return KebeleRegistrationSerializer
        return KebeleSerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class KetenaViewSet(viewsets.ModelViewSet):
    """API endpoint for managing ketenas."""
    queryset = Ketena.objects.all()
    serializer_class = KetenaSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageTenants]
    filterset_fields = ['kebele']

    def get_serializer_class(self):
        if self.action == 'create':
            return KetenaCreateSerializer
        return KetenaSerializer

    @transaction.atomic
    def perform_create(self, serializer):
        serializer.save()

class TenantRegistrationViewSet(viewsets.ViewSet):
    """API endpoint for tenant registration."""
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def available_cities(self, request):
        """Get all available cities for subcity registration."""
        from ..models.tenant import Tenant, TenantType
        cities = Tenant.objects.filter(type=TenantType.CITY)
        data = [{'id': city.id, 'name': city.name} for city in cities]
        return Response(data)

    @action(detail=False, methods=['get'])
    def available_subcities(self, request):
        """Get all available subcities for kebele registration."""
        from ..models.tenant import Tenant, TenantType
        city_id = request.query_params.get('city_id')
        queryset = Tenant.objects.filter(type=TenantType.SUBCITY)

        if city_id:
            queryset = queryset.filter(parent_id=city_id)

        subcities = queryset
        data = [{'id': subcity.id, 'name': subcity.name, 'parent_id': subcity.parent_id} for subcity in subcities]
        return Response(data)

    @action(detail=False, methods=['get'])
    def available_kebeles(self, request):
        """Get all available kebeles for ketena registration."""
        from ..models.tenant import Tenant, TenantType
        subcity_id = request.query_params.get('subcity_id')
        queryset = Tenant.objects.filter(type=TenantType.KEBELE)

        if subcity_id:
            queryset = queryset.filter(parent_id=subcity_id)

        kebeles = queryset
        data = [{'id': kebele.id, 'name': kebele.name, 'parent_id': kebele.parent_id} for kebele in kebeles]
        return Response(data)

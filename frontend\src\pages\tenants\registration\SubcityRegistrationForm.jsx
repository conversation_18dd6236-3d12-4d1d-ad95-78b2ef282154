import { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Divider,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { getStandardMenuProps } from '../../../utils/dropdownUtils';

const SubcityRegistrationForm = ({ data, onChange, parentOptions }) => {
  const handleChange = (e) => {
    const { name, value } = e.target;
    onChange({ ...data, [name]: value });
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    onChange({ ...data, [name]: checked });
  };

  const handleDateChange = (date) => {
    onChange({ ...data, established_date: date });
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight="bold">
        SubCity Information
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Enter the details for the subcity administration.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Basic Information</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="name"
            name="name"
            label="SubCity Name"
            value={data.name || ''}
            onChange={handleChange}
            placeholder="e.g., Bole"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="tenant_name"
            name="tenant_name"
            label="Tenant Name"
            value={data.tenant_name || data.name || ''}
            onChange={handleChange}
            placeholder="e.g., Bole SubCity Administration"
            helperText="Name used for the tenant schema"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            required
            id="domain_name"
            name="domain_name"
            label="Domain Name"
            value={data.domain_name || ''}
            onChange={handleChange}
            placeholder="e.g., bole.addisababa.goid.gov.et"
            helperText="Domain name for tenant access (without http://)"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth required>
            <InputLabel id="city-label">Parent City</InputLabel>
            <Select
              labelId="city-label"
              id="city_id"
              name="city_id"
              value={data.city_id || data.parentId || ''}
              label="Parent City"
              onChange={handleChange}
              MenuProps={getStandardMenuProps()}
            >
              {parentOptions.map((city) => (
                <MenuItem key={city.id} value={city.id}>
                  {city.name}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>Select the city this subcity belongs to</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="code"
            name="code"
            label="SubCity Code"
            value={data.code || ''}
            onChange={handleChange}
            placeholder="e.g., SC-01"
            helperText="Leave blank for auto-generation"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }}>Status</Divider>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={data.is_active !== false}
                onChange={handleSwitchChange}
                name="is_active"
                color="primary"
              />
            }
            label="Is Active"
          />
          <FormHelperText>Indicates whether the subcity is active</FormHelperText>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SubcityRegistrationForm;

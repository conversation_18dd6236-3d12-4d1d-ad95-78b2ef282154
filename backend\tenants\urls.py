from django.urls import path, include
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from django.utils import timezone
from django_tenants.utils import schema_context
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken
from rest_framework.routers import DefaultRouter
from .views import (
    TenantViewSet, DomainViewSet, switch_tenant,
    CityViewSet, SubcityViewSet, KebeleViewSet, KetenaViewSet,
    TenantRegistrationViewSet, CountryViewSet, RegionViewSet,
    ReligionViewSet, CitizenStatusViewSet, MaritalStatusViewSet,
    EmploymentTypeViewSet, DocumentViewSet,
    ParentViewSet, ChildViewSet, EmergencyContactViewSet,
    SpouseViewSet, CitizenViewSet, BiometricViewSet, PhotoViewSet,
    RelationshipViewSet, CurrentStatusViewSet
)
from .views.tenant_citizen_views import (
    TenantSpecificCitizenViewSet, TenantSpecificEmergencyContactViewSet,
    TenantSpecificParentViewSet, TenantSpecificChildViewSet,
    TenantSpecificSpouseViewSet, TenantSpecificDocumentViewSet
)
from .views.tenant_idcard_views import (
    TenantSpecificIDCardViewSet, TenantSpecificIDCardTemplateViewSet
)
from .views.transfer_views import CitizenTransferViewSet
from .views.clearance_views import CitizenClearanceViewSet

router = DefaultRouter()
router.register(r'domains', DomainViewSet)
router.register(r'cities', CityViewSet)
router.register(r'subcities', SubcityViewSet)
router.register(r'kebeles', KebeleViewSet)
router.register(r'ketenas', KetenaViewSet)
router.register(r'registration', TenantRegistrationViewSet, basename='registration')
router.register(r'countries', CountryViewSet)
router.register(r'regions', RegionViewSet)
router.register(r'', TenantViewSet, basename='tenant')
router.register(r'religions', ReligionViewSet)
router.register(r'citizen-statuses', CitizenStatusViewSet)
router.register(r'marital-statuses', MaritalStatusViewSet)
# document-types now handled by shared schema at /api/shared/document-types/
router.register(r'employment-types', EmploymentTypeViewSet)
router.register(r'documents', DocumentViewSet)
router.register(r'parents', ParentViewSet)
router.register(r'children', ChildViewSet)
router.register(r'emergency-contacts', EmergencyContactViewSet)
router.register(r'spouses', SpouseViewSet)
router.register(r'citizens', CitizenViewSet)
router.register(r'biometrics', BiometricViewSet)
router.register(r'photos', PhotoViewSet)
router.register(r'relationships', RelationshipViewSet)
router.register(r'current-statuses', CurrentStatusViewSet)


def create_transfer_request(request):
    """
    Simple function-based view to create transfer requests.
    Bypasses the complex ViewSet that's causing issues.
    """
    print(f"🔍 create_transfer_request called with method: {request.method}")
    print(f"🔍 Request path: {request.path}")
    print(f"🔍 Request headers: {dict(request.headers)}")
    print(f"🔍 Request user: {request.user}")
    print(f"🔍 User authenticated: {request.user.is_authenticated if hasattr(request, 'user') else 'NO_USER'}")

    if request.method == 'GET':
        return JsonResponse({'error': 'Only POST method allowed', 'method_received': request.method}, status=405)

    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed', 'method_received': request.method}, status=405)

    try:
        # Parse request data
        data = json.loads(request.body.decode('utf-8'))
        print(f"🔍 Transfer request data received: {data}")

        # Extract JWT token and get user/tenant info
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JsonResponse({'error': 'Authentication required'}, status=401)

        token = auth_header.split(' ')[1]
        jwt_auth = JWTAuthentication()

        try:
            validated_token = jwt_auth.get_validated_token(token)
            user = jwt_auth.get_user(validated_token)
            tenant_id = validated_token.get('tenant_id')
            print(f"🔍 Authenticated user: {user.email}, tenant_id: {tenant_id}")
        except InvalidToken:
            return JsonResponse({'error': 'Invalid token'}, status=401)

        # Get tenant and set schema context
        from tenants.models import Tenant
        try:
            tenant = Tenant.objects.get(id=tenant_id)
            print(f"🔍 Using tenant: {tenant.name} (schema: {tenant.schema_name})")
        except Tenant.DoesNotExist:
            return JsonResponse({'error': 'Tenant not found'}, status=404)

        # Create transfer request in tenant schema
        print(f"🔍 About to use schema context: {tenant.schema_name}")

        # Use django-tenants schema_context with explicit imports inside
        with schema_context(tenant.schema_name):
            from django.db import connection
            print(f"🔍 Inside schema_context - Current schema: {connection.schema_name}")

            # Import models inside the schema context
            from workflows.models import CitizenTransferRequest
            from tenants.models.citizen import Citizen

            print(f"🔍 Looking for citizen ID: {data['citizen_id']}")

            # Check what table we're actually querying
            print(f"🔍 Citizen model table: {Citizen._meta.db_table}")

            # Execute raw SQL to see what schema we're in
            with connection.cursor() as cursor:
                cursor.execute("SELECT current_schema()")
                current_schema = cursor.fetchone()[0]
                print(f"🔍 Raw SQL current_schema(): {current_schema}")

                # Check if citizen exists in current schema
                cursor.execute(f"SELECT id, first_name, last_name, digital_id FROM {Citizen._meta.db_table} WHERE id = %s", [data['citizen_id']])
                raw_citizen = cursor.fetchone()
                if raw_citizen:
                    print(f"🔍 Raw SQL found citizen: ID={raw_citizen[0]}, Name={raw_citizen[1]} {raw_citizen[2]}, Digital ID={raw_citizen[3]}")
                else:
                    print(f"❌ Raw SQL: No citizen with ID {data['citizen_id']} in {current_schema}.{Citizen._meta.db_table}")

            # List all citizens in current schema for debugging
            all_citizens = Citizen.objects.all()
            print(f"🔍 All citizens via ORM in schema ({current_schema}):")
            for c in all_citizens[:5]:  # Limit to first 5 for debugging
                print(f"  ID: {c.id} - {c.first_name} {c.last_name} - Digital ID: {c.digital_id}")

            # Get citizen
            try:
                citizen = Citizen.objects.get(id=data['citizen_id'])
                print(f"🔍 Found citizen via ORM: {citizen.first_name} {citizen.last_name} (Digital ID: {citizen.digital_id})")
            except Citizen.DoesNotExist:
                print(f"❌ Citizen ID {data['citizen_id']} not found via ORM in schema {current_schema}")
                return JsonResponse({'error': 'Citizen not found in tenant schema'}, status=404)

            # Get destination kebele
            try:
                destination_kebele = Tenant.objects.get(id=data['destination_kebele'])
                print(f"🔍 Destination kebele: {destination_kebele.name}")
            except Tenant.DoesNotExist:
                return JsonResponse({'error': 'Destination kebele not found'}, status=404)

            # Create transfer request
            transfer_request = CitizenTransferRequest.objects.create(
                citizen_id=citizen.id,
                citizen_name=f"{citizen.first_name} {citizen.last_name}",
                citizen_digital_id=citizen.digital_id,
                source_kebele=tenant,
                destination_kebele=destination_kebele,
                requested_by=user,
                transfer_reason=data.get('transfer_reason', 'relocation'),
                reason_description=data.get('reason_description', ''),
                status='pending'
            )

            print(f"✅ Transfer request created: ID {transfer_request.id}")

            return JsonResponse({
                'success': True,
                'message': 'Transfer request created successfully',
                'transfer_id': transfer_request.id,
                'citizen_name': f"{citizen.first_name} {citizen.last_name}",
                'destination_kebele': destination_kebele.name,
                'status': transfer_request.status
            })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        print(f"❌ Error creating transfer request: {e}")
        return JsonResponse({'error': f'Server error: {str(e)}'}, status=500)


urlpatterns = [
    path('', include(router.urls)),
    path('switch/', switch_tenant, name='switch-tenant'),

    # Tenant user management endpoints
    path('<int:pk>/users/', TenantViewSet.as_view({'get': 'users'}), name='tenant-users'),
    path('<int:pk>/create_user/', TenantViewSet.as_view({'post': 'create_user'}), name='tenant-create-user'),
    path('<int:pk>/update_user/', TenantViewSet.as_view({'patch': 'update_user'}), name='tenant-update-user'),
    path('<int:pk>/change_user_password/', TenantViewSet.as_view({'patch': 'change_user_password'}), name='tenant-change-user-password'),
    path('<int:pk>/create_admin/', TenantViewSet.as_view({'post': 'create_admin'}), name='tenant-create-admin'),

    # Tenant-specific citizen endpoints
    path('<int:tenant_id>/citizens/', TenantSpecificCitizenViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-citizens-list'),
    path('<int:tenant_id>/citizens/<int:pk>/', TenantSpecificCitizenViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-citizens-detail'),
    path('<int:tenant_id>/citizens/search/', TenantSpecificCitizenViewSet.as_view({
        'get': 'search'
    }), name='tenant-citizens-search'),
    path('<int:tenant_id>/citizens/stats/', TenantSpecificCitizenViewSet.as_view({
        'get': 'stats'
    }), name='tenant-citizens-stats'),
    path('<int:tenant_id>/idcards/stats/', TenantSpecificIDCardViewSet.as_view({
        'get': 'stats'
    }), name='tenant-idcards-stats'),

    # Tenant-specific ID card endpoints
    path('<int:tenant_id>/idcards/', TenantSpecificIDCardViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-idcards-list'),
    path('<int:tenant_id>/idcards/<int:pk>/', TenantSpecificIDCardViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-idcards-detail'),
    path('<int:tenant_id>/idcards/<int:pk>/approval_action/', TenantSpecificIDCardViewSet.as_view({
        'post': 'approval_action'
    }), name='tenant-idcards-approval-action'),
    path('<int:tenant_id>/idcards/<int:pk>/update_status/', TenantSpecificIDCardViewSet.as_view({
        'patch': 'update_status'
    }), name='tenant-idcards-update-status'),
    path('<int:tenant_id>/idcards/<int:pk>/print/', TenantSpecificIDCardViewSet.as_view({
        'post': 'print'
    }), name='tenant-idcards-print'),
    path('<int:tenant_id>/idcards/pending_subcity_approval/', TenantSpecificIDCardViewSet.as_view({
        'get': 'pending_subcity_approval'
    }), name='tenant-idcards-pending-subcity-approval'),
    path('<int:tenant_id>/idcards/printing_queue/', TenantSpecificIDCardViewSet.as_view({
        'get': 'printing_queue'
    }), name='tenant-idcards-printing-queue'),
    path('<int:tenant_id>/idcards/cross_tenant_list/', TenantSpecificIDCardViewSet.as_view({
        'get': 'cross_tenant_list'
    }), name='tenant-idcards-cross-tenant-list'),
    path('<int:tenant_id>/idcards/<int:pk>/cross_tenant_detail/', TenantSpecificIDCardViewSet.as_view({
        'get': 'cross_tenant_detail'
    }), name='tenant-idcards-cross-tenant-detail'),
    path('<int:tenant_id>/idcards/auth_test/', TenantSpecificIDCardViewSet.as_view({
        'get': 'auth_test'
    }), name='tenant-idcards-auth-test'),
    path('<int:tenant_id>/citizens/cross_tenant_list/', TenantSpecificCitizenViewSet.as_view({
        'get': 'cross_tenant_list'
    }), name='tenant-citizens-cross-tenant-list'),
    path('<int:tenant_id>/citizens/dashboard/reports/', TenantSpecificCitizenViewSet.as_view({
        'get': 'dashboard_reports'
    }), name='tenant-citizens-dashboard-reports'),
    path('<int:tenant_id>/idcards/templates/', TenantSpecificIDCardTemplateViewSet.as_view({
        'get': 'list'
    }), name='tenant-idcard-templates-list'),
    path('<int:tenant_id>/info/', TenantSpecificIDCardViewSet.as_view({
        'get': 'tenant_info'
    }), name='tenant-info'),

    # Citizen-specific family endpoints
    path('<int:tenant_id>/citizens/<int:pk>/spouse/', TenantSpecificCitizenViewSet.as_view({
        'get': 'spouse'
    }), name='tenant-citizen-spouse'),
    path('<int:tenant_id>/citizens/<int:pk>/parents/', TenantSpecificCitizenViewSet.as_view({
        'get': 'parents'
    }), name='tenant-citizen-parents'),
    path('<int:tenant_id>/citizens/<int:pk>/children/', TenantSpecificCitizenViewSet.as_view({
        'get': 'children'
    }), name='tenant-citizen-children'),
    path('<int:tenant_id>/citizens/<int:pk>/emergency-contacts/', TenantSpecificCitizenViewSet.as_view({
        'get': 'emergency_contacts'
    }), name='tenant-citizen-emergency-contacts'),

    # Tenant-specific family member endpoints
    path('<int:tenant_id>/emergency-contacts/', TenantSpecificEmergencyContactViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-emergency-contacts-list'),
    path('<int:tenant_id>/emergency-contacts/<int:pk>/', TenantSpecificEmergencyContactViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-emergency-contacts-detail'),

    path('<int:tenant_id>/parents/', TenantSpecificParentViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-parents-list'),
    path('<int:tenant_id>/parents/<int:pk>/', TenantSpecificParentViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-parents-detail'),

    path('<int:tenant_id>/children/', TenantSpecificChildViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-children-list'),
    path('<int:tenant_id>/children/<int:pk>/', TenantSpecificChildViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-children-detail'),

    path('<int:tenant_id>/spouses/', TenantSpecificSpouseViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-spouses-list'),
    path('<int:tenant_id>/spouses/<int:pk>/', TenantSpecificSpouseViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-spouses-detail'),

    # Tenant-specific document endpoints
    path('<int:tenant_id>/documents/', TenantSpecificDocumentViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='tenant-documents-list'),
    path('<int:tenant_id>/documents/<int:pk>/', TenantSpecificDocumentViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='tenant-documents-detail'),

    # TEST: Simple function-based view for debugging
    path('transfers/test/', lambda request: JsonResponse({'message': 'Test endpoint works!'}), name='transfer-test'),
    path('transfers/debug/', lambda request: JsonResponse({
        'message': 'Transfer debug endpoint reached!',
        'method': request.method,
        'user': str(request.user) if hasattr(request, 'user') else 'No user',
        'path': request.path
    }), name='transfer-debug'),

    # SIMPLE POST TEST - No authentication, no middleware complexity
    path('transfers/simple-test/', lambda request: JsonResponse({
        'message': 'Simple POST test works!',
        'method': request.method,
        'data': 'POST data received' if request.method == 'POST' else 'Not POST'
    }), name='transfer-simple-test'),

    # DIRECT TRANSFER ENDPOINT - Bypass ViewSet complexity
    path('transfers/direct/', csrf_exempt(lambda request: JsonResponse({
        'message': 'Direct transfer endpoint reached!',
        'method': request.method,
        'success': True,
        'data': json.loads(request.body.decode('utf-8')) if request.method == 'POST' and request.body else {}
    }) if request.method in ['GET', 'POST'] else JsonResponse({'error': 'Method not allowed'}, status=405)), name='transfer-direct'),

    # Citizen clearance endpoints
    path('clearances/', CitizenClearanceViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='citizen-clearances-list'),
    path('clearances/<int:pk>/', CitizenClearanceViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='citizen-clearances-detail'),
    path('clearances/<int:pk>/review/', CitizenClearanceViewSet.as_view({
        'post': 'review'
    }), name='citizen-clearances-review'),
    path('clearances/<int:pk>/issue/', CitizenClearanceViewSet.as_view({
        'post': 'issue'
    }), name='citizen-clearances-issue'),
    path('clearances/<int:pk>/upload-documents/', CitizenClearanceViewSet.as_view({
        'post': 'upload_documents'
    }), name='citizen-clearances-upload-documents'),
    path('clearances/<int:pk>/cancel/', CitizenClearanceViewSet.as_view({
        'post': 'cancel'
    }), name='citizen-clearances-cancel'),
    path('clearances/stats/', CitizenClearanceViewSet.as_view({
        'get': 'stats'
    }), name='citizen-clearances-stats'),

    # Citizen transfer endpoints - SPECIFIC PATTERNS FIRST
    path('transfers/available-kebeles/', CitizenTransferViewSet.as_view({
        'get': 'available_kebeles'
    }), name='citizen-transfers-available-kebeles'),
    path('transfers/stats/', CitizenTransferViewSet.as_view({
        'get': 'stats'
    }), name='citizen-transfers-stats'),

    # WORKING TRANSFER ENDPOINT - Actual implementation (BEFORE generic transfers/)
    path('transfers/create/', csrf_exempt(lambda request: create_transfer_request(request)), name='transfer-create'),

    # DEBUGGING ENDPOINT - Test if our function is called
    path('transfers/debug-create/', csrf_exempt(lambda request: JsonResponse({
        'message': 'DEBUG: Function called successfully!',
        'method': request.method,
        'body': request.body.decode('utf-8') if request.body else 'No body',
        'headers': dict(request.headers)
    })), name='transfer-debug-create'),

    # TEST ENDPOINT - Simple response to verify reachability
    path('citizen-transfer-test/', csrf_exempt(lambda request: JsonResponse({
        'message': 'Citizen transfer test endpoint reached!',
        'method': request.method,
        'path': request.path,
        'success': True,
        'headers': dict(request.headers),
        'user': str(request.user) if hasattr(request, 'user') else 'NO_USER'
    })), name='citizen-transfer-test'),

    # SIMPLE TEST ENDPOINT - Different path to avoid conflicts
    path('transfer-citizen-request/', csrf_exempt(lambda request: JsonResponse({
        'message': 'NEW Transfer endpoint reached!',
        'method': request.method,
        'path': request.path,
        'body': request.body.decode('utf-8') if request.body else 'No body',
        'headers': dict(request.headers),
        'user': str(request.user) if hasattr(request, 'user') else 'NO_USER',
        'success': True
    })), name='transfer-citizen-request'),

    # WORKING TRANSFER ENDPOINT - Completely unique path
    path('citizen-transfer-request/', csrf_exempt(lambda request: JsonResponse({
        'message': 'Transfer endpoint reached!',
        'method': request.method,
        'path': request.path,
        'body': request.body.decode('utf-8') if request.body else 'No body',
        'headers': dict(request.headers),
        'user': str(request.user) if hasattr(request, 'user') else 'NO_USER'
    }) if request.method == 'POST' else create_transfer_request(request)), name='citizen-transfer-request'),

    # SIMPLE TEST ENDPOINT - Bypass ViewSet complexity
    path('transfers/test/', csrf_exempt(lambda request: JsonResponse({
        'message': 'Simple transfer test endpoint works!',
        'method': request.method,
        'user': str(request.user),
        'authenticated': request.user.is_authenticated if hasattr(request, 'user') else False,
        'path': request.path,
        'success': True
    })), name='transfer-test'),

    # ALTERNATIVE PATH - Test if path-specific issue
    path('transfer-list/', CitizenTransferViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='citizen-transfers-list-alt'),

    # Transfer endpoints moved to separate transfer_urls.py to avoid conflicts
    path('transfers/<int:pk>/', CitizenTransferViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='citizen-transfers-detail'),
    path('transfers/<int:pk>/review/', CitizenTransferViewSet.as_view({
        'post': 'review'
    }), name='citizen-transfers-review'),
    path('transfers/<int:pk>/complete/', CitizenTransferViewSet.as_view({
        'post': 'complete'
    }), name='citizen-transfers-complete'),
    path('transfers/<int:pk>/cancel/', CitizenTransferViewSet.as_view({
        'post': 'cancel'
    }), name='citizen-transfers-cancel'),
]

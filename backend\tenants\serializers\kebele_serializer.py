from rest_framework import serializers
from django.db import transaction
from ..models.kebele import <PERSON>bel<PERSON>, Ketena
from ..models.subcity import SubCity
from ..models.tenant import Tenant, TenantType

class KebeleSerializer(serializers.ModelSerializer):
    subcity_name = serializers.CharField(source='sub_city.name', read_only=True)

    class Meta:
        model = Kebele
        fields = (
            'id', 'name', 'code', 'sub_city', 'subcity_name', 'is_active',
            'tenant', 'created_at', 'updated_at'
        )
        read_only_fields = ('code', 'created_at', 'updated_at')

class KebeleRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for registering a new kebele with its tenant."""
    tenant_name = serializers.CharField(write_only=True)
    subcity_id = serializers.IntegerField(write_only=True)
    tenant_id = serializers.IntegerField(read_only=True)
    domain_name = serializers.Char<PERSON>ield(write_only=True, required=False)  # Accept but ignore
    code = serializers.Char<PERSON>ield(write_only=True, required=False)  # Accept but ignore

    class Meta:
        model = Kebele
        fields = (
            'name', 'tenant_name', 'subcity_id', 'is_active', 'tenant_id', 'domain_name', 'code'
        )

    def validate_name(self, value):
        """Validate that the kebele name is unique."""
        if Kebele.objects.filter(name=value).exists():
            raise serializers.ValidationError(f"A kebele with the name '{value}' already exists.")
        return value

    def validate_subcity_id(self, value):
        """Validate that the subcity_id refers to a valid subcity tenant."""
        try:
            # Check if it's a tenant ID for a subcity
            subcity_tenant = Tenant.objects.get(id=value, type=TenantType.SUBCITY)
            # Check if the subcity has a SubCity profile
            subcity_profile = SubCity.objects.filter(tenant=subcity_tenant).first()
            if not subcity_profile:
                raise serializers.ValidationError("SubCity profile does not exist")
        except Tenant.DoesNotExist:
            raise serializers.ValidationError("SubCity tenant does not exist")
        return value

    @transaction.atomic
    def create(self, validated_data):
        from django.core.management import call_command
        from django_tenants.utils import schema_context
        from users.models import User

        tenant_name = validated_data.pop('tenant_name')
        subcity_tenant_id = validated_data.pop('subcity_id')
        # Extract domain_name before removing it
        user_domain_name = validated_data.pop('domain_name', None)
        # Remove fields that are not part of the Kebele model
        validated_data.pop('code', None)  # Remove if present

        # Get subcity tenant and subcity profile
        subcity_tenant = Tenant.objects.get(id=subcity_tenant_id, type=TenantType.SUBCITY)
        subcity_profile = SubCity.objects.get(tenant=subcity_tenant)

        # Generate schema name
        base_schema_name = f"kebele_{tenant_name.lower().replace(' ', '_')}"
        schema_name = base_schema_name

        # Ensure schema name is unique
        count = 1
        while Tenant.objects.filter(schema_name=schema_name).exists():
            schema_name = f"{base_schema_name}_{count}"
            count += 1

        # Create tenant
        tenant = Tenant.objects.create(
            name=tenant_name,
            type=TenantType.KEBELE,
            parent=subcity_tenant,
            schema_name=schema_name
        )

        # Create domain for the tenant using user-provided domain name
        from ..models.tenant import Domain
        # Use the user-provided domain_name if available, otherwise fall back to schema-based name
        if user_domain_name:
            domain_name = user_domain_name
        else:
            domain_name = f"{schema_name}.goid.local"
        Domain.objects.create(domain=domain_name, tenant=tenant, is_primary=True)

        # Run migrations for the new tenant schema
        with schema_context(schema_name):
            call_command('migrate', verbosity=0)

        # Create kebele profile
        # Only pass fields that exist in the Kebele model
        kebele_data = {
            'tenant': tenant,
            'name': validated_data.get('name'),
            'sub_city': subcity_profile,
            'is_active': validated_data.get('is_active', True)
        }
        kebele = Kebele.objects.create(**kebele_data)

        return kebele

    def to_representation(self, instance):
        """Add tenant_id and domain_name to the serialized output."""
        data = super().to_representation(instance)
        if instance.tenant:
            data['tenant_id'] = instance.tenant.id
            # Get domain name from tenant's domains
            domain = instance.tenant.domains.filter(is_primary=True).first()
            data['domain_name'] = domain.domain if domain else None
        # Remove fields that were only for input
        data.pop('code', None)
        return data

class KetenaSerializer(serializers.ModelSerializer):
    kebele_name = serializers.CharField(source='kebele.name', read_only=True)

    class Meta:
        model = Ketena
        fields = (
            'id', 'name', 'code', 'kebele', 'kebele_name', 'is_active',
            'created_at', 'updated_at'
        )
        read_only_fields = ('code', 'created_at', 'updated_at')

class KetenaCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating a new ketena."""
    kebele_id = serializers.IntegerField()

    class Meta:
        model = Ketena
        fields = (
            'name', 'kebele_id', 'is_active'
        )

    def validate_kebele_id(self, value):
        try:
            kebele = Kebele.objects.get(id=value)
        except Kebele.DoesNotExist:
            raise serializers.ValidationError("Kebele does not exist")
        return value

    def create(self, validated_data):
        kebele_id = validated_data.pop('kebele_id')

        # Get kebele
        kebele = Kebele.objects.get(id=kebele_id)

        # Create ketena
        ketena = Ketena.objects.create(
            name=validated_data.get('name'),
            kebele=kebele,
            **validated_data
        )

        return ketena

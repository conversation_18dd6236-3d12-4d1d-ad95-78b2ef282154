# Generated by Django 4.2.7 on 2025-05-22 17:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Citizen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('middle_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('last_name', models.Char<PERSON><PERSON>(max_length=100)),
                ('date_of_birth', models.DateField()),
                ('gender', models.Char<PERSON><PERSON>(choices=[('male', 'Male'), ('female', 'Female')], max_length=10)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='citizen_photos/')),
                ('phone_number', models.Char<PERSON><PERSON>(blank=True, max_length=15, null=True)),
                ('email', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=254, null=True)),
                ('house_number', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=20, null=True)),
                ('street', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('nationality', models.Char<PERSON>ield(default='Ethiopian', max_length=50)),
                ('marital_status', models.CharField(choices=[('single', 'Single'), ('married', 'Married'), ('divorced', 'Divorced'), ('widowed', 'Widowed')], default='single', max_length=10)),
                ('occupation', models.CharField(blank=True, max_length=100, null=True)),
                ('blood_type', models.CharField(choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-'), ('unknown', 'Unknown')], default='unknown', max_length=10)),
                ('emergency_contact_name', models.CharField(blank=True, max_length=200, null=True)),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=15, null=True)),
                ('emergency_contact_relation', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]

from rest_framework import serializers
from ..models.city import CityAdministration, Region, Country
from ..models.tenant import Tenant, TenantType

class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = ('id', 'name', 'code', 'capital_city')

class RegionSerializer(serializers.ModelSerializer):
    country_name = serializers.CharField(source='country.name', read_only=True)

    class Meta:
        model = Region
        fields = ('id', 'name', 'code', 'country', 'country_name')

class CityAdministrationSerializer(serializers.ModelSerializer):
    country_name = serializers.CharField(source='country.name', read_only=True)
    region_name = serializers.CharField(source='region.name', read_only=True)

    class Meta:
        model = CityAdministration
        fields = (
            'id', 'city_code', 'city_name', 'country', 'country_name', 'region', 'region_name',
            'logo', 'motto_slogan', 'city_intro', 'mayor_name', 'deputy_mayor',
            'contact_email', 'contact_phone', 'google_maps_url', 'area_sq_km',
            'elevation_meters', 'timezone', 'website', 'headquarter_address',
            'postal_code', 'established_date', 'is_resident', 'is_active',
            'tenant', 'created_at', 'updated_at'
        )
        read_only_fields = ('city_code', 'created_at', 'updated_at')

class CityRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for registering a new city with its tenant."""
    tenant_name = serializers.CharField(write_only=True)
    tenant_id = serializers.IntegerField(read_only=True)
    domain_name = serializers.CharField(write_only=True)

    class Meta:
        model = CityAdministration
        fields = (
            'city_name', 'tenant_name', 'country', 'region', 'logo', 'motto_slogan',
            'city_intro', 'mayor_name', 'deputy_mayor', 'contact_email', 'contact_phone',
            'google_maps_url', 'area_sq_km', 'elevation_meters', 'timezone', 'website',
            'headquarter_address', 'postal_code', 'established_date', 'is_resident',
            'is_active', 'tenant_id', 'domain_name'
        )

    def create(self, validated_data):
        import uuid
        from ..models.tenant import Domain

        tenant_name = validated_data.pop('tenant_name')
        # Extract domain_name before removing it
        user_domain_name = validated_data.pop('domain_name', None)
        user = self.context['request'].user

        # Create tenant
        tenant = Tenant.objects.create(
            name=tenant_name,
            type=TenantType.CITY,
            schema_name=f"city_{tenant_name.lower().replace(' ', '_')}"
        )

        # Create domain for the tenant using user-provided domain name
        # Use the user-provided domain_name if available, otherwise fall back to schema-based name
        if user_domain_name:
            domain_name = user_domain_name
        else:
            domain_name = f"{tenant.schema_name}.goid.local"
        Domain.objects.create(
            domain=domain_name,
            tenant=tenant,
            is_primary=True
        )

        # Generate unique city code
        city_name = validated_data.get('city_name', tenant_name)
        city_code = f"CITY_{city_name[:4].upper()}_{str(uuid.uuid4())[:8].upper()}"

        # Ensure uniqueness
        while CityAdministration.objects.filter(city_code=city_code).exists():
            city_code = f"CITY_{city_name[:4].upper()}_{str(uuid.uuid4())[:8].upper()}"

        # Create city profile
        city = CityAdministration.objects.create(
            tenant=tenant,
            city_code=city_code,
            **validated_data
        )

        # Add tenant and domain info to the response
        city.tenant_id = tenant.id
        city.domain_name = domain_name

        return city

    def to_representation(self, instance):
        """Add tenant_id and domain_name to the serialized output."""
        data = super().to_representation(instance)
        if instance.tenant:
            data['tenant_id'] = instance.tenant.id
            # Get domain name from tenant's domains
            domain = instance.tenant.domains.filter(is_primary=True).first()
            data['domain_name'] = domain.domain if domain else None
        return data

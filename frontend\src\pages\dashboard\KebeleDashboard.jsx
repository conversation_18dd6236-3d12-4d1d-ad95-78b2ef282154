import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  Divider
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  ResponsiveContainer
} from 'recharts';
import {
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Badge as BadgeIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

const KebeleDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      console.log('🚨 KebeleDashboard: Starting to fetch dashboard data...');
      setLoading(true);

      // Check if we have a token
      const token = localStorage.getItem('accessToken');
      console.log('🚨 KebeleDashboard: Token exists:', !!token);

      console.log('🚨 KebeleDashboard: Calling dashboard endpoint...');
      const response = await axios.get('/api/kebele-dashboard/');
      console.log('🚨 KebeleDashboard: Response received:', response.data);
      setDashboardData(response.data.data);
      setError(null);
    } catch (error) {
      console.error('🚨 KebeleDashboard: Error fetching dashboard data:', error);
      console.error('🚨 KebeleDashboard: Error response:', error.response);
      setError(`Failed to load dashboard data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No dashboard data available
      </Alert>
    );
  }

  // Big Number Card Component
  const BigNumberCard = ({ title, value, icon, color = 'primary', trend = null }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="h6">
              {title}
            </Typography>
            <Typography variant="h3" component="div" color={color}>
              {value?.toLocaleString() || 0}
            </Typography>
            {trend && (
              <Box display="flex" alignItems="center" mt={1}>
                <TrendingUpIcon color="success" fontSize="small" />
                <Typography variant="body2" color="success.main" ml={0.5}>
                  {trend}
                </Typography>
              </Box>
            )}
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  // Chart Card Component
  const ChartCard = ({ title, children, height = 300 }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Box height={height}>
          {children}
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          {dashboardData.tenant_info?.name} Dashboard
        </Typography>
        <Typography variant="subtitle1" color="textSecondary">
          Last updated: {new Date(dashboardData.last_updated).toLocaleString()}
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Row 1: Big Number Cards */}
        <Grid item xs={12} sm={6} md={3}>
          <BigNumberCard
            title="Total Citizens"
            value={dashboardData.total_citizens?.count}
            icon={<PeopleIcon sx={{ fontSize: 40 }} />}
            color="primary"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <BigNumberCard
            title="New This Month"
            value={dashboardData.new_registrations_this_month?.count || 0}
            icon={<PersonAddIcon sx={{ fontSize: 40 }} />}
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <BigNumberCard
            title="Expiring IDs"
            value={dashboardData.expiring_ids_next_30_days?.count || 0}
            icon={<BadgeIcon sx={{ fontSize: 40 }} />}
            color="warning"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <BigNumberCard
            title="Expired IDs"
            value={dashboardData.expired_ids_over_30_days || 0}
            icon={<BadgeIcon sx={{ fontSize: 40 }} />}
            color="error"
          />
        </Grid>

        {/* Row 2: Registration Trend */}
        <Grid item xs={12}>
          <ChartCard title="Registration Trend (Last 12 Months)" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dashboardData.total_citizens?.trend || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  name="New Registrations"
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        {/* Row 3: Population by Ketena and Gender Ratio */}
        <Grid item xs={12} md={8}>
          <ChartCard title="Population by Ketena" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart 
                data={dashboardData.population_by_ketena || []}
                layout="horizontal"
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="ketena" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <ChartCard title="Gender Distribution" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={dashboardData.gender_ratio || []}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  nameKey="gender"
                >
                  {(dashboardData.gender_ratio || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        {/* Row 4: Age Groups and Blood Type */}
        <Grid item xs={12} md={6}>
          <ChartCard title="Age Group Distribution" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={dashboardData.age_group_distribution || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="age_group" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <ChartCard title="Blood Type Distribution" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={dashboardData.blood_type_distribution || []}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="count"
                  nameKey="blood_type"
                >
                  {(dashboardData.blood_type_distribution || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        {/* Row 5: ID Status and New Registrations Daily */}
        <Grid item xs={12} md={6}>
          <ChartCard title="ID Status Summary" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={dashboardData.id_status_summary || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="status" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <ChartCard title="New Registrations This Month (Daily)" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dashboardData.new_registrations_this_month?.daily_trend || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="count"
                  stroke="#82ca9d"
                  strokeWidth={2}
                  name="New Registrations"
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        {/* Row 6: Expiring IDs and Migration */}
        <Grid item xs={12} md={6}>
          <ChartCard title="Expiring IDs in Next 30 Days" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={dashboardData.expiring_ids_next_30_days?.daily_trend || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="count"
                  stroke="#ff7c7c"
                  fill="#ff7c7c"
                  fillOpacity={0.6}
                  name="Expiring IDs"
                />
              </AreaChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <ChartCard title="Migration Summary" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={[
                { type: 'Transfers In', count: dashboardData.migration_data?.transfers_in || 0 },
                { type: 'Transfers Out', count: dashboardData.migration_data?.transfers_out || 0 },
                { type: 'Clearances', count: dashboardData.migration_data?.clearances_issued || 0 },
                { type: 'Net Migration', count: dashboardData.migration_data?.net_migration || 0 }
              ]}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        {/* Row 7: Disability and Marital Status */}
        <Grid item xs={12} md={6}>
          <ChartCard title="Disability Status" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={dashboardData.disability_status || []}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="count"
                  nameKey="disability_status"
                >
                  {(dashboardData.disability_status || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <ChartCard title="Marital Status" height={300}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={dashboardData.marital_status || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="marital_status" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </ChartCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default KebeleDashboard;

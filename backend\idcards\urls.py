from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import IDCardViewSet, IDCardTemplateViewSet
from .services_views import (
    apply_renewal_service,
    apply_replacement_service,
    apply_reprint_service,
    get_service_requests,
    approve_service_request,
    reject_service_request,
    check_renewal_eligibility
)

router = DefaultRouter()
router.register(r'', IDCardViewSet)
router.register(r'templates', IDCardTemplateViewSet)

urlpatterns = [
    path('', include(router.urls)),

    # ID Card Services
    path('services/renewal/apply/', apply_renewal_service, name='apply-renewal'),
    path('services/replacement/apply/', apply_replacement_service, name='apply-replacement'),
    path('services/reprint/apply/', apply_reprint_service, name='apply-reprint'),
    path('services/requests/', get_service_requests, name='service-requests'),
    path('services/requests/<int:request_id>/approve/', approve_service_request, name='approve-request'),
    path('services/requests/<int:request_id>/reject/', reject_service_request, name='reject-request'),
    path('services/renewal/check/<str:digital_id>/', check_renewal_eligibility, name='check-renewal'),
]

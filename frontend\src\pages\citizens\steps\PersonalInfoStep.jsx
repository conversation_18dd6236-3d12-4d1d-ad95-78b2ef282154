import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Person as PersonIcon } from '@mui/icons-material';
import SharedDataDropdown from '../../../components/common/SharedDataDropdown';
import { getStandardMenuProps, createSelectChangeHandler } from '../../../utils/dropdownUtils';

const PersonalInfoStep = ({ formik, loading }) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="h2">
              Personal Information
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="first_name"
                name="first_name"
                label="First Name"
                value={formik.values.first_name}
                onChange={formik.handleChange}
                error={formik.touched.first_name && Boolean(formik.errors.first_name)}
                helperText={formik.touched.first_name && formik.errors.first_name}
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="middle_name"
                name="middle_name"
                label="Middle Name"
                value={formik.values.middle_name}
                onChange={formik.handleChange}
                error={formik.touched.middle_name && Boolean(formik.errors.middle_name)}
                helperText={formik.touched.middle_name && formik.errors.middle_name}
                required
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="last_name"
                name="last_name"
                label="Last Name"
                value={formik.values.last_name}
                onChange={formik.handleChange}
                error={formik.touched.last_name && Boolean(formik.errors.last_name)}
                helperText={formik.touched.last_name && formik.errors.last_name}
                required
              />
            </Grid>

            {/* Amharic Names */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="first_name_am"
                name="first_name_am"
                label="First Name (Amharic)"
                value={formik.values.first_name_am}
                onChange={formik.handleChange}
                error={formik.touched.first_name_am && Boolean(formik.errors.first_name_am)}
                helperText={formik.touched.first_name_am && formik.errors.first_name_am}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="middle_name_am"
                name="middle_name_am"
                label="Middle Name (Amharic)"
                value={formik.values.middle_name_am}
                onChange={formik.handleChange}
                error={formik.touched.middle_name_am && Boolean(formik.errors.middle_name_am)}
                helperText={formik.touched.middle_name_am && formik.errors.middle_name_am}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                id="last_name_am"
                name="last_name_am"
                label="Last Name (Amharic)"
                value={formik.values.last_name_am}
                onChange={formik.handleChange}
                error={formik.touched.last_name_am && Boolean(formik.errors.last_name_am)}
                helperText={formik.touched.last_name_am && formik.errors.last_name_am}
              />
            </Grid>

            {/* Date of Birth and Gender */}
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Date of Birth"
                value={formik.values.date_of_birth}
                onChange={(value) => formik.setFieldValue('date_of_birth', value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: formik.touched.date_of_birth && Boolean(formik.errors.date_of_birth),
                    helperText: formik.touched.date_of_birth && formik.errors.date_of_birth,
                    required: true
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.gender && Boolean(formik.errors.gender)} required>
                <InputLabel id="gender-label">Gender</InputLabel>
                <Select
                  labelId="gender-label"
                  id="gender"
                  name="gender"
                  value={formik.values.gender}
                  label="Gender"
                  onChange={formik.handleChange}
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
                {formik.touched.gender && formik.errors.gender && (
                  <FormHelperText>{formik.errors.gender}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* Additional Information */}
            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="countries"
                label="Nationality"
                name="nationality"
                id="nationality"
                value={formik.values.nationality}
                onChange={formik.handleChange}
                error={formik.touched.nationality && formik.errors.nationality}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="citizenStatuses"
                label="Citizen Status"
                name="citizen_status"
                id="citizen_status"
                value={formik.values.citizen_status}
                onChange={formik.handleChange}
                error={formik.touched.citizen_status && formik.errors.citizen_status}
                disabled={loading}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="religions"
                label="Religion"
                name="religion"
                id="religion"
                value={formik.values.religion}
                onChange={formik.handleChange}
                error={formik.touched.religion && formik.errors.religion}
                disabled={loading}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <SharedDataDropdown
                dataType="maritalStatuses"
                label="Marital Status"
                name="marital_status"
                id="marital_status"
                value={formik.values.marital_status}
                onChange={formik.handleChange}
                error={formik.touched.marital_status && formik.errors.marital_status}
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.disability && Boolean(formik.errors.disability)}>
                <InputLabel id="disability-label">Disability Status</InputLabel>
                <Select
                  labelId="disability-label"
                  id="disability"
                  name="disability"
                  value={formik.values.disability}
                  label="Disability Status"
                  onChange={createSelectChangeHandler('disability', formik.handleChange)}
                  MenuProps={getStandardMenuProps()}
                >
                  <MenuItem value="none">None</MenuItem>
                  <MenuItem value="physical">Physical Disability</MenuItem>
                  <MenuItem value="visual">Visual Impairment</MenuItem>
                  <MenuItem value="hearing">Hearing Impairment</MenuItem>
                  <MenuItem value="intellectual">Intellectual Disability</MenuItem>
                  <MenuItem value="mental">Mental Health Condition</MenuItem>
                  <MenuItem value="multiple">Multiple Disabilities</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
                {formik.touched.disability && formik.errors.disability && (
                  <FormHelperText>{formik.errors.disability}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.blood_type && Boolean(formik.errors.blood_type)}>
                <InputLabel id="blood-type-label">Blood Type</InputLabel>
                <Select
                  labelId="blood-type-label"
                  id="blood_type"
                  name="blood_type"
                  value={formik.values.blood_type}
                  label="Blood Type"
                  onChange={createSelectChangeHandler('blood_type', formik.handleChange)}
                  MenuProps={getStandardMenuProps()}
                >
                  <MenuItem value="A+">A+</MenuItem>
                  <MenuItem value="A-">A-</MenuItem>
                  <MenuItem value="B+">B+</MenuItem>
                  <MenuItem value="B-">B-</MenuItem>
                  <MenuItem value="AB+">AB+</MenuItem>
                  <MenuItem value="AB-">AB-</MenuItem>
                  <MenuItem value="O+">O+</MenuItem>
                  <MenuItem value="O-">O-</MenuItem>
                  <MenuItem value="unknown">Unknown</MenuItem>
                </Select>
                {formik.touched.blood_type && formik.errors.blood_type && (
                  <FormHelperText>{formik.errors.blood_type}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
};

export default PersonalInfoStep;

# Generated by Django 4.2.7 on 2025-05-22 17:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('idcards', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkflowLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('submit', 'Submit'), ('approve', 'Approve'), ('reject', 'Reject'), ('print', 'Print'), ('issue', 'Issue'), ('revoke', 'Revoke')], max_length=20)),
                ('from_status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], max_length=20)),
                ('to_status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], max_length=20)),
                ('comment', models.TextField(blank=True, null=True)),
                ('performed_at', models.DateTimeField(auto_now_add=True)),
                ('id_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='workflow_logs', to='idcards.idcard')),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-performed_at'],
            },
        ),
    ]

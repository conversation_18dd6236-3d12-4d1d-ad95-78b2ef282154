# Generated by Django 4.2.7 on 2025-05-22 17:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('citizens', '0002_initial'),
        ('idcards', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='idcard',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='citizen',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='id_cards', to='citizens.citizen'),
        ),
        migrations.AddField(
            model_name='idcard',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='issued_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='issued_id_cards', to=settings.AUTH_USER_MODEL),
        ),
    ]

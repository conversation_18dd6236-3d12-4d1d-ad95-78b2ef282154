# Generated manually for data conversion

from django.db import migrations
import uuid


def convert_citizen_data(apps, schema_editor):
    """Convert existing citizen data to new format"""
    Citizen = apps.get_model('citizens', 'Citizen')
    
    # Mapping for marital status text to ID
    marital_status_mapping = {
        'single': 1,
        'married': 2,
        'divorced': 3,
        'widowed': 4,
        'separated': 5,
    }
    
    # Process all citizens
    for citizen in Citizen.objects.all():
        # Generate digital_id if not exists
        if not citizen.digital_id:
            citizen.digital_id = str(uuid.uuid4())[:8].upper()
        
        # Convert marital_status from text to integer
        if citizen.marital_status:
            marital_text = citizen.marital_status.lower().strip()
            if marital_text in marital_status_mapping:
                citizen.marital_status_new = marital_status_mapping[marital_text]
        
        # Copy phone_number to phone
        if hasattr(citizen, 'phone_number') and citizen.phone_number:
            citizen.phone = citizen.phone_number
        
        # Convert nationality if it's text (assuming it might be country name)
        # For now, set to None - this can be manually updated later
        citizen.nationality_new = None
        
        # Copy occupation to employment fields
        if hasattr(citizen, 'occupation') and citizen.occupation:
            citizen.employment = citizen.occupation
            citizen.employee_type = citizen.occupation
        
        citizen.save()


def reverse_convert_citizen_data(apps, schema_editor):
    """Reverse the data conversion"""
    Citizen = apps.get_model('citizens', 'Citizen')
    
    # Reverse mapping for marital status
    reverse_marital_mapping = {
        1: 'single',
        2: 'married', 
        3: 'divorced',
        4: 'widowed',
        5: 'separated',
    }
    
    for citizen in Citizen.objects.all():
        # Convert marital_status back to text
        if citizen.marital_status_new:
            citizen.marital_status = reverse_marital_mapping.get(citizen.marital_status_new, '')
        
        # Clear new fields
        citizen.digital_id = ''
        citizen.phone = ''
        citizen.marital_status_new = None
        citizen.nationality_new = None
        citizen.employment = ''
        citizen.employee_type = ''
        citizen.organization_name = ''
        
        citizen.save()


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0003_add_new_fields'),
    ]

    operations = [
        migrations.RunPython(convert_citizen_data, reverse_convert_citizen_data),
    ]

# Generated by Django 4.2.7 on 2025-05-31 16:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('tenants', '0010_alter_document_document_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='transfer_date',
            field=models.DateTimeField(blank=True, help_text='Date when citizen was transferred', null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_reason',
            field=models.CharField(blank=True, help_text='Reason for transfer if citizen was transferred', max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='CitizenTransferRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_id', models.Char<PERSON>ield(editable=False, max_length=20, unique=True)),
                ('citizen_id', models.Integer<PERSON>ield(help_text='ID of citizen in source kebele')),
                ('citizen_name', models.CharField(help_text='Full name of citizen', max_length=255)),
                ('citizen_digital_id', models.CharField(blank=True, max_length=50, null=True)),
                ('transfer_reason', models.CharField(choices=[('relocation', 'Relocation/Moving'), ('marriage', 'Marriage'), ('work', 'Work/Employment'), ('education', 'Education'), ('family', 'Family Reasons'), ('other', 'Other')], default='relocation', max_length=20)),
                ('reason_description', models.TextField(blank=True, help_text='Detailed reason for transfer', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('completed', 'Transfer Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('review_notes', models.TextField(blank=True, help_text='Notes from destination kebele leader', null=True)),
                ('new_citizen_id', models.IntegerField(blank=True, help_text='ID of citizen in destination kebele after transfer', null=True)),
                ('completed_by', models.ForeignKey(blank=True, help_text='Kebele A leader who completed the transfer', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfer_requests_completed', to=settings.AUTH_USER_MODEL)),
                ('destination_kebele', models.ForeignKey(help_text='Kebele where citizen wants to transfer', on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transfers', to='tenants.tenant')),
                ('requested_by', models.ForeignKey(help_text='Kebele A leader who initiated the request', on_delete=django.db.models.deletion.CASCADE, related_name='transfer_requests_made', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Kebele B leader who reviewed the request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfer_requests_reviewed', to=settings.AUTH_USER_MODEL)),
                ('source_kebele', models.ForeignKey(help_text='Kebele where citizen currently resides', on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_transfers', to='tenants.tenant')),
            ],
            options={
                'db_table': 'citizen_transfer_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CitizenTransferHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_citizen_data', models.JSONField(help_text='Complete citizen data from source kebele')),
                ('transfer_date', models.DateTimeField(auto_now_add=True)),
                ('source_kebele_name', models.CharField(max_length=255)),
                ('destination_kebele_name', models.CharField(max_length=255)),
                ('transfer_year', models.IntegerField()),
                ('transfer_month', models.IntegerField()),
                ('transfer_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='tenants.citizentransferrequest')),
            ],
            options={
                'db_table': 'citizen_transfer_history',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['status'], name='citizen_tra_status_65438c_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['source_kebele', 'status'], name='citizen_tra_source__93d763_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['destination_kebele', 'status'], name='citizen_tra_destina_5797c3_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferrequest',
            index=models.Index(fields=['created_at'], name='citizen_tra_created_50067a_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferhistory',
            index=models.Index(fields=['transfer_year', 'transfer_month'], name='citizen_tra_transfe_6d7f7f_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferhistory',
            index=models.Index(fields=['source_kebele_name'], name='citizen_tra_source__a7fa98_idx'),
        ),
        migrations.AddIndex(
            model_name='citizentransferhistory',
            index=models.Index(fields=['destination_kebele_name'], name='citizen_tra_destina_eaa082_idx'),
        ),
    ]

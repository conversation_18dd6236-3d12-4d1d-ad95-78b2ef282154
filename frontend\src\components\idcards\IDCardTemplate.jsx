import React from 'react';
import { Box, Typography, Avatar } from '@mui/material';

const IDCardTemplate = ({ idCard, side = 'front', preview = false }) => {
  if (!idCard) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px dashed #ccc',
          borderRadius: 2
        }}
      >
        <Typography color="text.secondary">No ID card data</Typography>
      </Box>
    );
  }

  const citizen = idCard.citizen || {};
  const tenantHierarchy = idCard.tenant_hierarchy || {};

  // Get security pattern class for visual patterns
  const getSecurityPatternClass = () => {
    if (idCard.has_kebele_pattern && idCard.has_subcity_pattern) {
      return 'full-pattern'; // Both patterns applied
    } else if (idCard.has_kebele_pattern) {
      return 'half-pattern'; // Only kebele pattern
    }
    return 'no-pattern'; // No patterns
  };

  const securityPatternClass = getSecurityPatternClass();

  // Card dimensions
  const cardStyle = {
    width: preview ? '500px' : '100%',
    height: preview ? '315px' : '200px',
    position: 'relative',
    backgroundColor: '#ffffff',
    border: '2px solid #1976d2',
    borderRadius: '12px',
    overflow: 'hidden',
    fontFamily: 'Arial, sans-serif',
    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
  };

  // Security pattern overlay
  const getSecurityPattern = () => {
    if (securityPatternClass === 'no-pattern') return null;

    const patternStyle = {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: 0.15,
      pointerEvents: 'none',
      zIndex: 1
    };

    if (securityPatternClass === 'half-pattern') {
      // Left half pattern (kebele approved)
      return (
        <Box
          sx={{
            ...patternStyle,
            background: `
              linear-gradient(45deg,
                transparent 25%,
                #1976d2 25%,
                #1976d2 50%,
                transparent 50%,
                transparent 75%,
                #1976d2 75%
              )
            `,
            backgroundSize: '20px 20px',
            width: '50%'
          }}
        />
      );
    } else if (securityPatternClass === 'full-pattern') {
      // Full pattern (both kebele and subcity approved)
      return (
        <Box
          sx={{
            ...patternStyle,
            background: `
              linear-gradient(45deg,
                transparent 25%,
                #1976d2 25%,
                #1976d2 50%,
                transparent 50%,
                transparent 75%,
                #1976d2 75%
              )
            `,
            backgroundSize: '20px 20px'
          }}
        />
      );
    }
  };

  if (side === 'front') {
    return (
      <Box sx={cardStyle}>
        {getSecurityPattern()}

        {/* Header */}
        <Box
          sx={{
            backgroundColor: '#1976d2',
            color: 'white',
            p: 1,
            textAlign: 'center',
            position: 'relative',
            zIndex: 2
          }}
        >
          <Typography variant="h6" sx={{ fontSize: preview ? '14px' : '12px', fontWeight: 'bold' }}>
            የኢትዮጵያ ፌዴራላዊ ዲሞክራሲያዊ ሪፐብሊክ
          </Typography>
          <Typography variant="body2" sx={{ fontSize: preview ? '12px' : '10px' }}>
            Federal Democratic Republic of Ethiopia
          </Typography>
          <Typography variant="body1" sx={{ fontSize: preview ? '13px' : '11px', fontWeight: 'bold' }}>
            {tenantHierarchy.city_tenant || 'ጎንደር'} ከተማ - {tenantHierarchy.parent_tenant || 'ዞብል'} ክፍለ ከተማ - {tenantHierarchy.current_tenant || 'ገብርኤል'} ቀበሌ
          </Typography>
          <Typography variant="body2" sx={{ fontSize: preview ? '11px' : '9px' }}>
            Digital Identity Card
          </Typography>
        </Box>

        {/* Content */}
        <Box sx={{ p: 2, display: 'flex', position: 'relative', zIndex: 2 }}>
          {/* Photo */}
          <Box sx={{ mr: 2 }}>
            <Avatar
              src={citizen.photo}
              sx={{
                width: preview ? 80 : 60,
                height: preview ? 100 : 75,
                borderRadius: 1,
                border: '2px solid #1976d2'
              }}
            >
              {citizen.first_name?.[0]}{citizen.last_name?.[0]}
            </Avatar>

            {/* ID Number under photo */}
            <Typography
              variant="body2"
              sx={{
                fontSize: preview ? '11px' : '9px',
                fontWeight: 'bold',
                textAlign: 'center',
                mt: 1,
                fontFamily: 'monospace'
              }}
            >
              {idCard.card_number || 'ID-000000'}
            </Typography>
          </Box>

          {/* Details */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontSize: preview ? '14px' : '12px', fontWeight: 'bold', mb: 1 }}>
              {citizen.first_name} {citizen.middle_name} {citizen.last_name}
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2" sx={{ fontSize: preview ? '11px' : '9px', fontWeight: 'bold', mr: 1 }}>
                ጾታ/Gender:
              </Typography>
              <Typography variant="body2" sx={{ fontSize: preview ? '11px' : '9px' }}>
                {citizen.gender === 'M' ? 'ወንድ/Male' : citizen.gender === 'F' ? 'ሴት/Female' : 'N/A'}
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 0.5 }}>
              <strong>የትውልድ ቀን/DOB:</strong> {citizen.date_of_birth || 'N/A'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 0.5 }}>
              <strong>ዲጂታል መታወቂያ/Digital ID:</strong> {citizen.digital_id || 'N/A'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 0.5 }}>
              <strong>የመኖሪያ አድራሻ/Address:</strong> {citizen.address || 'N/A'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px' }}>
              <strong>የስልክ ቁጥር/Phone:</strong> {citizen.phone_number || 'N/A'}
            </Typography>
          </Box>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: '#f5f5f5',
            p: 0.5,
            textAlign: 'center',
            zIndex: 2
          }}
        >
          <Typography variant="caption" sx={{ fontSize: preview ? '9px' : '7px' }}>
            Issue Date: {idCard.issue_date || 'N/A'} | Expires: {idCard.expiry_date || 'N/A'}
          </Typography>
        </Box>
      </Box>
    );
  } else {
    // Back side
    return (
      <Box sx={cardStyle}>
        {getSecurityPattern()}

        {/* Header */}
        <Box
          sx={{
            backgroundColor: '#1976d2',
            color: 'white',
            p: 1,
            textAlign: 'center',
            position: 'relative',
            zIndex: 2
          }}
        >
          <Typography variant="h6" sx={{ fontSize: preview ? '14px' : '12px', fontWeight: 'bold' }}>
            Digital Identity Card - Back
          </Typography>
        </Box>

        {/* Content */}
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', position: 'relative', zIndex: 2 }}>
          {/* Left side - Additional info */}
          <Box sx={{ flex: 1, mr: 2 }}>
            <Typography variant="body2" sx={{ fontSize: preview ? '11px' : '9px', mb: 1, fontWeight: 'bold' }}>
              Additional Information:
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 0.5 }}>
              <strong>Marital Status:</strong> {citizen.marital_status || 'N/A'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 0.5 }}>
              <strong>Religion:</strong> {citizen.religion || 'N/A'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 0.5 }}>
              <strong>Nationality:</strong> {citizen.nationality || 'Ethiopian'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 1 }}>
              <strong>Emergency Contact:</strong> {citizen.emergency_contact_phone || 'N/A'}
            </Typography>

            <Typography variant="body2" sx={{ fontSize: preview ? '9px' : '7px', fontStyle: 'italic' }}>
              This card is property of the Federal Democratic Republic of Ethiopia.
              If found, please return to the nearest government office.
            </Typography>
          </Box>

          {/* Right side - QR Code */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Typography variant="body2" sx={{ fontSize: preview ? '10px' : '8px', mb: 1, fontWeight: 'bold' }}>
              QR Code
            </Typography>
            <Box
              sx={{
                width: preview ? 80 : 60,
                height: preview ? 80 : 60,
                border: '2px solid #1976d2',
                borderRadius: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5'
              }}
            >
              <Typography variant="caption" sx={{ fontSize: '8px', textAlign: 'center' }}>
                QR<br/>CODE
              </Typography>
            </Box>
            <Typography variant="caption" sx={{ fontSize: preview ? '8px' : '6px', mt: 0.5, textAlign: 'center' }}>
              Scan for verification
            </Typography>
          </Box>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: '#f5f5f5',
            p: 0.5,
            textAlign: 'center',
            zIndex: 2
          }}
        >
          <Typography variant="caption" sx={{ fontSize: preview ? '9px' : '7px' }}>
            Card ID: {idCard.uuid || 'N/A'} | Status: {idCard.status || 'N/A'}
          </Typography>
        </Box>
      </Box>
    );
  }
};

export default IDCardTemplate;

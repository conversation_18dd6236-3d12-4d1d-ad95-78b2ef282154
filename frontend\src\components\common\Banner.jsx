import { Box, Typography, useTheme, Paper, alpha } from '@mui/material';
import { useState, useEffect } from 'react';

const Banner = ({ title, subtitle, icon }) => {
  const theme = useTheme();
  const [animate, setAnimate] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimate(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Paper
      elevation={0}
      sx={{
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 0, // Removed border radius for full-width effect
        mb: 2,
        background: `
          linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.85)} 0%, ${alpha(theme.palette.primary.dark, 0.85)} 100%),
          url('/images/banner-bg.svg')
        `,
        backgroundSize: 'cover',
        backgroundPosition: 'center right',
        backgroundRepeat: 'no-repeat',
        boxShadow: `0 20px 40px -15px ${alpha(theme.palette.primary.main, 0.4)}`,
        transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
        transform: animate ? 'translateY(0)' : 'translateY(15px)',
        opacity: animate ? 1 : 0.7,
        border: theme.palette.mode === 'light'
          ? `1px solid ${alpha(theme.palette.primary.light, 0.2)}`
          : `1px solid ${alpha(theme.palette.primary.dark, 0.5)}`,
        minHeight: { xs: 80, sm: 90, md: 100 }, // Minimized height
      }}
    >
      {/* Decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: -50,
          right: -30,
          width: 200,
          height: 200,
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha('#fff', 0.15)} 0%, ${alpha('#fff', 0)} 70%)`,
          filter: 'blur(5px)',
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -60,
          left: -40,
          width: 250,
          height: 250,
          borderRadius: '50%',
          background: `radial-gradient(circle, ${alpha('#fff', 0.15)} 0%, ${alpha('#fff', 0)} 70%)`,
          filter: 'blur(5px)',
        }}
      />

      {/* Floating particles */}
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          right: '10%',
          width: 8,
          height: 8,
          borderRadius: '50%',
          background: alpha('#fff', 0.6),
          boxShadow: `0 0 20px 2px ${alpha('#fff', 0.3)}`,
          animation: 'float 6s ease-in-out infinite',
          '@keyframes float': {
            '0%, 100%': { transform: 'translateY(0px)' },
            '50%': { transform: 'translateY(-20px)' },
          },
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          right: '20%',
          width: 12,
          height: 12,
          borderRadius: '50%',
          background: alpha('#fff', 0.4),
          boxShadow: `0 0 15px 1px ${alpha('#fff', 0.2)}`,
          animation: 'float 8s ease-in-out infinite 1s',
          '@keyframes float': {
            '0%, 100%': { transform: 'translateY(0px)' },
            '50%': { transform: 'translateY(-20px)' },
          },
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '30%',
          left: '15%',
          width: 6,
          height: 6,
          borderRadius: '50%',
          background: alpha('#fff', 0.5),
          boxShadow: `0 0 10px 1px ${alpha('#fff', 0.25)}`,
          animation: 'float 7s ease-in-out infinite 0.5s',
          '@keyframes float': {
            '0%, 100%': { transform: 'translateY(0px)' },
            '50%': { transform: 'translateY(-15px)' },
          },
        }}
      />

      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 1,
          p: { xs: 2, md: 3 },
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: { xs: 'flex-start', sm: 'center' },
          height: '100%', // Fill the container height
        }}
      >
        {icon && (
          <Box
            sx={{
              mr: { xs: 0, sm: 4 },
              mb: { xs: 2, sm: 0 },
              color: 'white',
              fontSize: { xs: '2rem', md: '2.5rem' },
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: alpha('#fff', 0.15),
              borderRadius: '50%',
              p: 2,
              width: { xs: 50, sm: 60, md: 70 },
              height: { xs: 50, sm: 60, md: 70 },
              boxShadow: `0 10px 20px ${alpha('#000', 0.1)}`,
              backdropFilter: 'blur(5px)',
              border: `1px solid ${alpha('#fff', 0.2)}`,
              transition: 'all 0.4s ease',
              animation: animate ? 'pulse 2s infinite' : 'none',
              '@keyframes pulse': {
                '0%': { boxShadow: `0 0 0 0 ${alpha('#fff', 0.4)}` },
                '70%': { boxShadow: `0 0 0 10px ${alpha('#fff', 0)}` },
                '100%': { boxShadow: `0 0 0 0 ${alpha('#fff', 0)}` },
              },
            }}
          >
            {icon}
          </Box>
        )}
        <Box>
          <Typography
            variant="h4"
            component="h1"
            fontWeight="bold"
            sx={{
              color: 'white',
              textShadow: '0 2px 10px rgba(0,0,0,0.2)',
              mb: 1,
              transition: 'all 0.4s ease-in-out',
              transform: animate ? 'translateY(0)' : 'translateY(10px)',
              letterSpacing: '-0.02em',
            }}
          >
            {title}
          </Typography>
          {subtitle && subtitle.trim() && (
            <Typography
              variant="h6"
              sx={{
                color: 'white',
                opacity: 0.9,
                textShadow: '0 2px 8px rgba(0,0,0,0.15)',
                transition: 'all 0.4s ease-in-out',
                transitionDelay: '0.15s',
                transform: animate ? 'translateY(0)' : 'translateY(10px)',
                opacity: animate ? 0.9 : 0.6,
                fontWeight: 400,
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

export default Banner;

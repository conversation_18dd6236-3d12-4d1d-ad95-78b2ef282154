from django.db import models
import uuid


class IDCardStatus(models.TextChoices):
    DRAFT = 'draft', 'Draft'
    PENDING_APPROVAL = 'pending_approval', 'Pending Kebele Approval'
    KEBELE_APPROVED = 'kebele_approved', 'Kebele Approved - Pending Subcity Approval'
    APPROVED = 'approved', 'Fully Approved - Ready for Printing'
    REJECTED = 'rejected', 'Rejected'
    PRINTED = 'printed', 'Printed'
    ISSUED = 'issued', 'Issued'
    EXPIRED = 'expired', 'Expired'
    REVOKED = 'revoked', 'Revoked'


class IDCardTemplate(models.Model):
    """
    ID Card Template model for tenant-specific templates.
    Each tenant can have multiple templates with different designs.
    """
    name = models.Char<PERSON>ield(max_length=100, help_text="Template name (e.g., 'Default Template', 'Special Edition')")
    description = models.TextField(blank=True, help_text="Template description")

    # Template design settings
    background_color = models.Char<PERSON><PERSON>(max_length=7, default="#FFFFFF", help_text="Background color in hex format")
    text_color = models.CharField(max_length=7, default="#000000", help_text="Text color in hex format")
    accent_color = models.CharField(max_length=7, default="#1976D2", help_text="Accent color in hex format")

    # Template layout settings
    logo_position = models.CharField(
        max_length=20,
        choices=[
            ('top_left', 'Top Left'),
            ('top_center', 'Top Center'),
            ('top_right', 'Top Right'),
        ],
        default='top_left'
    )
    photo_position = models.CharField(
        max_length=20,
        choices=[
            ('left', 'Left Side'),
            ('right', 'Right Side'),
            ('center', 'Center'),
        ],
        default='left'
    )

    # Template content
    header_text = models.CharField(max_length=200, default="Federal Democratic Republic of Ethiopia")
    subtitle_text = models.CharField(max_length=200, default="National ID Card")
    footer_text = models.CharField(max_length=200, blank=True, help_text="Optional footer text")

    # Template status
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False, help_text="Default template for this tenant")

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} {'(Default)' if self.is_default else ''}"

    def save(self, *args, **kwargs):
        # Ensure only one default template per tenant
        if self.is_default:
            IDCardTemplate.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class IDCard(models.Model):
    # Card Information
    card_number = models.CharField(max_length=50, unique=True, editable=False)
    citizen = models.ForeignKey('citizens.Citizen', on_delete=models.CASCADE, related_name='id_cards')
    template = models.ForeignKey(IDCardTemplate, on_delete=models.SET_NULL, null=True, blank=True, help_text="ID Card template to use")
    issue_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=IDCardStatus.choices, default=IDCardStatus.DRAFT)

    # QR Code and Security
    qr_code = models.ImageField(upload_to='id_card_qr_codes/', blank=True, null=True)
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)

    # Approval workflow fields
    # Kebele level approval
    kebele_approval_comment = models.TextField(blank=True, null=True, help_text="Comment from kebele leader during approval")
    submitted_for_approval_at = models.DateTimeField(null=True, blank=True, help_text="When the ID card was submitted for kebele approval")
    kebele_approved_at = models.DateTimeField(null=True, blank=True, help_text="When the ID card was approved by kebele leader")
    kebele_approved_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='kebele_approved_id_cards')

    # Subcity level approval
    subcity_approval_comment = models.TextField(blank=True, null=True, help_text="Comment from subcity admin during final approval")
    submitted_to_subcity_at = models.DateTimeField(null=True, blank=True, help_text="When the ID card was submitted to subcity for final approval")
    subcity_approved_at = models.DateTimeField(null=True, blank=True, help_text="When the ID card was finally approved by subcity admin")
    subcity_approved_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='subcity_approved_id_cards')

    # Security patterns
    has_kebele_pattern = models.BooleanField(default=False, help_text="Whether kebele approval pattern (left half) is applied")
    has_subcity_pattern = models.BooleanField(default=False, help_text="Whether subcity approval pattern (right half) is applied")

    # System fields
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, related_name='created_id_cards')
    issued_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='issued_id_cards')
    printed_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='printed_id_cards', help_text="User who printed the ID card")
    printed_at = models.DateTimeField(null=True, blank=True, help_text="When the ID card was printed")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # PDF file
    pdf_file = models.FileField(upload_to='id_card_pdfs/', blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"ID Card: {self.card_number} - {self.citizen.get_full_name()}"

    def save(self, *args, **kwargs):
        # Generate card number if not set
        if not self.card_number:
            # Format: ETH-[TENANT_CODE]-[RANDOM_6_DIGITS]
            from django.db import connection

            # Get a shorter tenant code, handle FakeTenant safely
            tenant_name = getattr(connection.tenant, 'name', 'UNKNOWN').upper()
            tenant_type = getattr(connection.tenant, 'type', 'TENANT').upper()
            tenant_code = f"{tenant_type[:1]}{tenant_name[:3]}"  # e.g., KBOL for Kebele Bole

            random_digits = str(uuid.uuid4().int)[:6]
            self.card_number = f"ETH-{tenant_code}-{random_digits}"

        super().save(*args, **kwargs)

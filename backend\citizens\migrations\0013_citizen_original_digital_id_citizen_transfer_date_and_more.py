# Generated by Django 4.2.7 on 2025-06-05 07:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0012_alter_document_document_type_delete_documenttype'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='original_digital_id',
            field=models.CharField(blank=True, help_text='Original digital ID before transfer', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_date',
            field=models.DateTimeField(blank=True, help_text='Date when citizen was transferred', null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_destination_kebele_id',
            field=models.IntegerField(blank=True, help_text='ID of kebele citizen was transferred to', null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_destination_kebele_name',
            field=models.Char<PERSON><PERSON>(blank=True, help_text='Name of kebele citizen was transferred to', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_reason',
            field=models.CharField(blank=True, help_text='Reason for transfer', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_request_id',
            field=models.CharField(blank=True, help_text='ID of the transfer request', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='citizen',
            name='transfer_status',
            field=models.CharField(choices=[('active', 'Active'), ('transferred', 'Transferred'), ('deceased', 'Deceased'), ('inactive', 'Inactive')], default='active', help_text='Current status of the citizen', max_length=20),
        ),
    ]

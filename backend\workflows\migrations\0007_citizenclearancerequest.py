# Generated by Django 4.2.7 on 2025-06-05 14:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0013_remove_citizen_models_from_public'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('workflows', '0006_add_transfer_documents'),
    ]

    operations = [
        migrations.CreateModel(
            name='CitizenClearanceRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clearance_id', models.CharField(editable=False, max_length=20, unique=True)),
                ('citizen_id', models.IntegerField(help_text='ID of citizen in kebele')),
                ('citizen_name', models.CharField(help_text='Full name of citizen', max_length=255)),
                ('citizen_digital_id', models.CharField(blank=True, max_length=50, null=True)),
                ('destination_location', models.Char<PERSON><PERSON>(help_text='Where the citizen is moving to (city, region, country)', max_length=255)),
                ('clearance_reason', models.CharField(choices=[('relocation_outside_city', 'Relocation Outside City'), ('marriage_outside_city', 'Marriage Outside City'), ('work_outside_city', 'Work/Employment Outside City'), ('education_outside_city', 'Education Outside City'), ('family_outside_city', 'Family Reasons Outside City'), ('emigration', 'Emigration/Moving Abroad'), ('other', 'Other')], default='relocation_outside_city', max_length=30)),
                ('reason_description', models.TextField(help_text='Detailed reason for requesting clearance')),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('issued', 'Clearance Letter Issued'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('issued_at', models.DateTimeField(blank=True, null=True)),
                ('application_letter', models.FileField(blank=True, help_text='Application letter from citizen requesting clearance', null=True, upload_to='clearance_documents/application_letters/')),
                ('current_kebele_id', models.FileField(blank=True, help_text='Current kebele ID card of the citizen', null=True, upload_to='clearance_documents/kebele_ids/')),
                ('supporting_documents', models.FileField(blank=True, help_text='Additional supporting documents (job offer, marriage certificate, etc.)', null=True, upload_to='clearance_documents/supporting/')),
                ('documents_uploaded_at', models.DateTimeField(blank=True, help_text='When documents were uploaded', null=True)),
                ('review_notes', models.TextField(blank=True, help_text='Notes from subcity admin', null=True)),
                ('clearance_letter_path', models.CharField(blank=True, help_text='Path to generated clearance letter PDF', max_length=500, null=True)),
                ('issued_by', models.ForeignKey(blank=True, help_text='User who issued the clearance letter', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clearance_requests_issued', to=settings.AUTH_USER_MODEL)),
                ('requested_by', models.ForeignKey(help_text='Kebele leader who initiated the request', on_delete=django.db.models.deletion.CASCADE, related_name='clearance_requests_made', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, help_text='Subcity admin who reviewed the request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clearance_requests_reviewed', to=settings.AUTH_USER_MODEL)),
                ('source_kebele', models.ForeignKey(help_text='Kebele where citizen currently resides', on_delete=django.db.models.deletion.CASCADE, related_name='clearance_requests', to='tenants.tenant')),
            ],
            options={
                'db_table': 'citizen_clearance_requests',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['status'], name='citizen_cle_status_c2f404_idx'), models.Index(fields=['source_kebele', 'status'], name='citizen_cle_source__0c120b_idx'), models.Index(fields=['created_at'], name='citizen_cle_created_1448ca_idx'), models.Index(fields=['citizen_id'], name='citizen_cle_citizen_bdb68a_idx')],
            },
        ),
    ]

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Paper,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Description as DescriptionIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Assignment as IssueIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';

const ClearanceDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [clearance, setClearance] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewAction, setReviewAction] = useState('');
  const [reviewNotes, setReviewNotes] = useState('');

  useEffect(() => {
    fetchClearanceDetails();
  }, [id]);

  const fetchClearanceDetails = async () => {
    try {
      const response = await axios.get(`/api/tenants/clearances/${id}/`);
      setClearance(response.data);
    } catch (error) {
      console.error('Error fetching clearance details:', error);
      setError('Failed to load clearance details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'info';
      case 'rejected':
        return 'error';
      case 'issued':
        return 'success';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'approved':
        return '✅';
      case 'rejected':
        return '❌';
      case 'issued':
        return '📄';
      case 'cancelled':
        return '🚫';
      default:
        return '❓';
    }
  };

  const handleReviewOpen = (action) => {
    setReviewAction(action);
    setReviewNotes('');
    setReviewDialogOpen(true);
  };

  const handleReviewSubmit = async () => {
    if (reviewAction === 'reject' && !reviewNotes.trim()) {
      alert('Review notes are required when rejecting a clearance request.');
      return;
    }

    try {
      await axios.post(`/api/tenants/clearances/${id}/review/`, {
        action: reviewAction,
        review_notes: reviewNotes.trim()
      });
      
      setReviewDialogOpen(false);
      fetchClearanceDetails();
      alert(`Clearance ${reviewAction}d successfully!`);
    } catch (error) {
      console.error(`Error ${reviewAction}ing clearance:`, error);
      alert(`Failed to ${reviewAction} clearance. Please try again.`);
    }
  };

  const handleIssue = async () => {
    if (!window.confirm('Are you sure you want to issue the clearance letter?')) {
      return;
    }

    try {
      await axios.post(`/api/tenants/clearances/${id}/issue/`);
      
      fetchClearanceDetails();
      alert('Clearance letter issued successfully!');
    } catch (error) {
      console.error('Error issuing clearance:', error);
      alert('Failed to issue clearance letter. Please try again.');
    }
  };

  const canReview = () => {
    return user?.role === 'subcity_admin' && clearance?.status === 'pending';
  };

  const canIssue = () => {
    return (user?.role === 'kebele_leader' || user?.role === 'clerk') && 
           clearance?.status === 'approved';
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !clearance) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error">
          {error || 'Clearance not found'}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/clearances')}
          sx={{ mr: 2 }}
        >
          Back to Clearances
        </Button>
        <Typography variant="h4" component="h1">
          Clearance Request Details
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Details */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2">
                  {clearance.clearance_id}
                </Typography>
                <Chip
                  label={`${getStatusIcon(clearance.status)} ${clearance.status_display}`}
                  color={getStatusColor(clearance.status)}
                  size="large"
                />
              </Box>

              <Divider sx={{ mb: 3 }} />

              {/* Citizen Information */}
              <Box sx={{ mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <PersonIcon color="primary" />
                  <Typography variant="h6" fontWeight="bold">
                    Citizen Information
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Full Name
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {clearance.citizen_name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Digital ID
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {clearance.citizen_digital_id || 'Not assigned'}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              {/* Clearance Details */}
              <Box sx={{ mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <LocationIcon color="primary" />
                  <Typography variant="h6" fontWeight="bold">
                    Clearance Details
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Destination Location
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {clearance.destination_location}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="text.secondary">
                      Reason
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {clearance.clearance_reason_display}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Detailed Reason
                    </Typography>
                    <Typography variant="body1">
                      {clearance.reason_description}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              {/* Review Notes */}
              {clearance.review_notes && (
                <Box sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <DescriptionIcon color="primary" />
                    <Typography variant="h6" fontWeight="bold">
                      Review Notes
                    </Typography>
                  </Box>
                  <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                    <Typography variant="body1">
                      {clearance.review_notes}
                    </Typography>
                    {clearance.reviewed_by_info && (
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        Reviewed by: {clearance.reviewed_by_info.first_name} {clearance.reviewed_by_info.last_name} 
                        {clearance.reviewed_at && ` on ${new Date(clearance.reviewed_at).toLocaleDateString()}`}
                      </Typography>
                    )}
                  </Paper>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Actions Sidebar */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Actions
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {canReview() && (
                  <>
                    <Button
                      variant="contained"
                      color="success"
                      startIcon={<ApproveIcon />}
                      onClick={() => handleReviewOpen('approve')}
                      fullWidth
                    >
                      Approve Clearance
                    </Button>
                    <Button
                      variant="contained"
                      color="error"
                      startIcon={<RejectIcon />}
                      onClick={() => handleReviewOpen('reject')}
                      fullWidth
                    >
                      Reject Clearance
                    </Button>
                  </>
                )}

                {canIssue() && (
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<IssueIcon />}
                    onClick={handleIssue}
                    fullWidth
                  >
                    Issue Clearance Letter
                  </Button>
                )}

                {clearance.status === 'issued' && clearance.clearance_letter_path && (
                  <Button
                    variant="outlined"
                    startIcon={<PrintIcon />}
                    onClick={() => {
                      // Generate download URL for the clearance letter
                      const downloadUrl = `${process.env.REACT_APP_API_URL || 'http://10.139.8.141:8000'}/media/${clearance.clearance_letter_path}`;

                      // Create download link
                      const link = document.createElement('a');
                      link.href = downloadUrl;
                      link.download = `clearance_letter_${clearance.clearance_id}.txt`;
                      link.target = '_blank';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    fullWidth
                  >
                    Download Clearance Letter
                  </Button>
                )}
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Timeline */}
              <Typography variant="h6" gutterBottom>
                Timeline
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2">
                  <strong>Created:</strong> {new Date(clearance.created_at).toLocaleString()}
                </Typography>
                {clearance.reviewed_at && (
                  <Typography variant="body2">
                    <strong>Reviewed:</strong> {new Date(clearance.reviewed_at).toLocaleString()}
                  </Typography>
                )}
                {clearance.issued_at && (
                  <Typography variant="body2">
                    <strong>Issued:</strong> {new Date(clearance.issued_at).toLocaleString()}
                  </Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {reviewAction === 'approve' ? 'Approve Clearance' : 'Reject Clearance'}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Review Notes"
            multiline
            rows={4}
            value={reviewNotes}
            onChange={(e) => setReviewNotes(e.target.value)}
            placeholder={reviewAction === 'reject' ? 'Please provide reason for rejection...' : 'Optional notes about the approval...'}
            required={reviewAction === 'reject'}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleReviewSubmit}
            variant="contained"
            color={reviewAction === 'approve' ? 'success' : 'error'}
          >
            {reviewAction === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ClearanceDetails;

# Generated by Django 4.2.7 on 2025-05-29 15:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('idcards', '0007_add_approval_fields'),
    ]

    operations = [
        migrations.RenameField(
            model_name='idcard',
            old_name='approval_comment',
            new_name='kebele_approval_comment',
        ),
        migrations.RemoveField(
            model_name='idcard',
            name='approval_pattern',
        ),
        migrations.RemoveField(
            model_name='idcard',
            name='approved_at',
        ),
        migrations.RemoveField(
            model_name='idcard',
            name='approved_by',
        ),
        migrations.AddField(
            model_name='idcard',
            name='has_kebele_pattern',
            field=models.BooleanField(default=False, help_text='Whether kebele approval pattern (left half) is applied'),
        ),
        migrations.AddField(
            model_name='idcard',
            name='has_subcity_pattern',
            field=models.BooleanField(default=False, help_text='Whether subcity approval pattern (right half) is applied'),
        ),
        migrations.AddField(
            model_name='idcard',
            name='kebele_approved_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was approved by kebele leader', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='kebele_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='kebele_approved_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='subcity_approval_comment',
            field=models.TextField(blank=True, help_text='Comment from subcity admin during final approval', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='subcity_approved_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was finally approved by subcity admin', null=True),
        ),
        migrations.AddField(
            model_name='idcard',
            name='subcity_approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subcity_approved_id_cards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='idcard',
            name='submitted_to_subcity_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was submitted to subcity for final approval', null=True),
        ),
        migrations.AlterField(
            model_name='idcard',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Kebele Approval'), ('kebele_approved', 'Kebele Approved - Pending Subcity Approval'), ('approved', 'Fully Approved - Ready for Printing'), ('rejected', 'Rejected'), ('printed', 'Printed'), ('issued', 'Issued'), ('expired', 'Expired'), ('revoked', 'Revoked')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='idcard',
            name='submitted_for_approval_at',
            field=models.DateTimeField(blank=True, help_text='When the ID card was submitted for kebele approval', null=True),
        ),
    ]

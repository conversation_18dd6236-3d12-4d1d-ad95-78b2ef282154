# Generated by Django 4.2.7 on 2025-05-25 08:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0005_increase_city_code_length'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='parent',
            options={'verbose_name': 'Parent', 'verbose_name_plural': 'Parents'},
        ),
        migrations.AddField(
            model_name='parent',
            name='citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='parents', to='tenants.citizen'),
        ),
        migrations.AddField(
            model_name='parent',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='parent',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='parent',
            name='is_resident',
            field=models.BooleanField(default=False, help_text='Indicates whether the parent is a resident of the city.'),
        ),
        migrations.AddField(
            model_name='parent',
            name='linked_citizen',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='as_parent', to='tenants.citizen'),
        ),
        migrations.AddField(
            model_name='parent',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
    ]
